FROM golang:1.23.2-alpine AS builder
WORKDIR /build
RUN apk add --no-cache gcc musl-dev
COPY . .
# enable cgo
ENV CGO_ENABLED=1
RUN go build -o ./bin/forum ./cmd/forum/main.go

# final image
FROM alpine
WORKDIR /app
RUN apk add --no-cache bash sqlite-libs
COPY --from=builder /build/bin/forum ./forum
COPY --from=builder /build/internal/db ./internal/db
COPY --from=builder /build/static ./static


EXPOSE 8080

CMD ["make", "full"]