package errors

import (
	"html/template"
	"log"
	"net/http"
	"path/filepath"
	"time"
)

type LoggingResponseWriter struct {
	http.ResponseWriter
	statusCode int
}

func NewLoggingResponseWriter(w http.ResponseWriter) *LoggingResponseWriter {
	return &LoggingResponseWriter{w, http.StatusOK}
}

func (lrw *LoggingResponseWriter) WriteHeader(code int) {
	lrw.statusCode = code
	lrw.ResponseWriter.WriteHeader(code)
}

func LoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		lrw := NewLoggingResponseWriter(w)
		next.ServeHTTP(lrw, r)
		log.Printf("%s %s %d %s", r.Method, r.URL.Path, lrw.statusCode, time.Since(start))
	})
}

type ErrorData struct {
	StatusCode int
	StatusText string
	Message    string
}

func ShowError(w http.ResponseWriter, r *http.Request, status int, message string) {
	w.<PERSON>rite<PERSON>eader(status)
	
	tmpl, err := template.ParseFiles(filepath.Join("static", "templates", "error.html"))
	if err != nil {
		log.Printf("Error parsing template: %v", err)
		http.Error(w, http.StatusText(status), status)
		return
	}
	
	data := ErrorData{
		StatusCode: status,
		StatusText: http.StatusText(status),
		Message:    message,
	}
	
	if err := tmpl.Execute(w, data); err != nil {
		log.Printf("Error executing template: %v", err)
		http.Error(w, http.StatusText(status), status)
	}
}

// NotFoundHandler handles 404 errors
func NotFoundHandler(w http.ResponseWriter, r *http.Request) {
	ShowError(w, r, http.StatusNotFound, "The page you're looking for doesn't exist.")
}

// UnauthorizedHandler handles 401 errors
func UnauthorizedHandler(w http.ResponseWriter, r *http.Request) {
	ShowError(w, r, http.StatusUnauthorized, "You are not authorized to access this page.")
}

// ForbiddenHandler handles 403 errors
func ForbiddenHandler(w http.ResponseWriter, r *http.Request) {
	ShowError(w, r, http.StatusForbidden, "You don't have permission to access this resource.")
}

// MethodNotAllowedHandler handles 405 errors
func MethodNotAllowedHandler(w http.ResponseWriter, r *http.Request) {
	ShowError(w, r, http.StatusMethodNotAllowed, "This method is not allowed for the requested resource.")
}

// InternalErrorHandler handles 500 errors
func InternalErrorHandler(w http.ResponseWriter, r *http.Request, err error) {
	log.Printf("Internal Server Error: %v", err)
	ShowError(w, r, http.StatusInternalServerError, "Something went wrong on our end. Please try again later.")
}

// BadRequestHandler handles 400 errors
func BadRequestHandler(w http.ResponseWriter, r *http.Request, message string) {
	if message == "" {
		message = "Your request could not be processed."
	}
	ShowError(w, r, http.StatusBadRequest, message)
}
