package handlers

import (
	"forum/internal/cookie"
	"forum/internal/db"
	"forum/internal/errors"
	"net/http"
	"time"
)

func PostHandler(w http.ResponseWriter, r *http.Request) {

	if !cookie.IsAuthenticated(r) {
		cookie.DeleteCookie(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	if r.Method != http.MethodPost {
		errors.MethodNotAllowedHandler(w, r)
		return
	}

	// check if user is logged in
	userID, err := db.GetUserID(r)
	if err != nil {
		http.Redirect(w, r, "/home", http.StatusSeeOther)
		return
	}

	// Parse form data
	err = r.ParseForm()
	if err != nil {
		http.Error(w, "Error parsing form data", http.StatusBadRequest)
		return
	}

	// Get form values
	title := r.FormValue("title")
	content := r.FormValue("content")
	date := time.Now()
	categories := r.Form["categories"]

	// Basic validation
	if title == "" || content == "" {
		http.Error(w, "All fields are required", http.StatusBadRequest)
		return
	}

	newPost := db.Post{
		UserID:     userID,
		Title:      title,
		Content:    content,
		Date:       date,
		Categories: categories,
	}

	posterror := newPost.InsertPost(categories)
	if posterror != nil {
		http.Error(w, "Error creating a new post", http.StatusInternalServerError)
		return
	}

	http.Redirect(w, r, "/home", http.StatusSeeOther)

}
