package handlers

import (
	"forum/internal/db"
	"forum/internal/errors"
	"net/http"
)

func Root(w http.ResponseWriter, r *http.Request) {

	if r.Method != http.MethodGet {
		errors.MethodNotAllowedHandler(w, r)
		return
	}

	userID, _ := db.GetUserID(r)

	switch userID {
	case -1:
		http.Redirect(w, r, "/home", http.StatusSeeOther)
	case -2:
		http.Redirect(w, r, "/login", http.StatusSeeOther)
	default:
		http.Redirect(w, r, "/home", http.StatusSeeOther)
	}
}
