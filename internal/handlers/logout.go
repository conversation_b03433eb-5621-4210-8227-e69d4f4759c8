package handlers

import (
	"forum/internal/cookie"
	"forum/internal/db"
	"net/http"
)

func LogoutHandler(w http.ResponseWriter, r *http.Request) {
	userID, err := db.GetUserID(r)

	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
	} else {
		err := db.Logout(userID)
		if err != nil {
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		}
	}

	cookie.DeleteCookie(w, r)
	http.Redirect(w, r, "/home", http.StatusSeeOther)

}
