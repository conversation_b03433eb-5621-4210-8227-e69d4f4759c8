package handlers

import (
	"forum/internal/cookie"
	"forum/internal/db"
	"forum/internal/errors"
	Renderer "forum/internal/server/renderer"
	"net/http"
)

func FilterHandler(w http.ResponseWriter, r *http.Request) {
	if !cookie.IsAuthenticated(r) {
		cookie.DeleteCookie(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	if r.Method != http.MethodGet {
		errors.MethodNotAllowedHandler(w, r)
		return
	}

	var posts []db.Post
	var err error

	// Parse form data
	err = r.ParseForm()
	if err != nil {
		errors.BadRequestHandler(w, r, "Invalid form data")
		return
	}

	// Check if we have category names from checkboxes
	selectedCategories := r.Form["categories"] // This gets all selected checkbox values

	// Handle multiple categories by name
	posts, err = db.GetPostsByCategoryNames(selectedCategories)
	if err != nil {
		errors.InternalErrorHandler(w, r, err)
		return
	}

	

	// Get categories for the template
	categories, err := db.GetAllCategories()
	if err != nil {
		errors.InternalErrorHandler(w, r, err)
		return
	}

	data := struct {
		Template           string
		IsAuthenticated    bool
		Posts              []db.Post
		Categories         []db.Category
		SelectedCategories []string
	}{
		Template:           "index",
		IsAuthenticated:    cookie.IsAuthenticated(r),
		Posts:              posts,
		Categories:         categories,
		SelectedCategories: selectedCategories,
	}

	Renderer.RenderTemplate(w, "layout", data)
}
