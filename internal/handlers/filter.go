package handlers

import (
	"forum/internal/cookie"
	"forum/internal/db"
	"forum/internal/errors"
	Renderer "forum/internal/server/renderer"
	"net/http"
	"strconv"
)

func FilterHandler(w http.ResponseWriter, r *http.Request) {
	if !cookie.IsAuthenticated(r) {
		cookie.DeleteCookie(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	if r.Method != http.MethodGet {
		errors.MethodNotAllowedHandler(w, r)
		return
	}

	var posts []db.Post
	var err error

	// Parse form data
	err = r.ParseForm()
	if err != nil {
		errors.BadRequestHandler(w, r, "Invalid form data")
		return
	}

	// Check if we have category names from checkboxes
	selectedCategories := r.Form["categories"] // This gets all selected checkbox values

	// Check if we have a single category ID (for direct links)
	categoryIDStr := r.FormValue("categoryID")

	if len(selectedCategories) > 0 {
		// Handle multiple categories by name
		posts, err = getPostsByCategoryNames(selectedCategories)
	} else if categoryIDStr != "" {
		// Handle single category by ID (for backwards compatibility)
		categoryID, parseErr := strconv.Atoi(categoryIDStr)
		if parseErr != nil {
			errors.BadRequestHandler(w, r, "Invalid category ID")
			return
		}
		posts, err = db.GetPostsByCategory(categoryID)
	} else {
		// No filters selected, redirect to home
		http.Redirect(w, r, "/home", http.StatusSeeOther)
		return
	}

	if err != nil {
		errors.InternalErrorHandler(w, r, err)
		return
	}

	// Get categories for the template
	categories, err := db.GetAllCategories()
	if err != nil {
		errors.InternalErrorHandler(w, r, err)
		return
	}

	// Add icons to categories
	for i := range categories {
		categories[i].Icon = getCategoryIcon(categories[i].CatName)
	}

	data := struct {
		Template           string
		IsAuthenticated    bool
		Posts              []db.Post
		Categories         []db.Category
		SelectedCategories []string
	}{
		Template:           "index",
		IsAuthenticated:    cookie.IsAuthenticated(r),
		Posts:              posts,
		Categories:         categories,
		SelectedCategories: selectedCategories, // To keep checkboxes checked
	}

	Renderer.RenderTemplate(w, "layout", data)
}

func getPostsByCategoryNames(categoryNames []string) ([]db.Post, error) {
	if len(categoryNames) == 0 {
		return []db.Post{}, nil
	}

	// If only one category, use the existing function
	if len(categoryNames) == 1 {
		category, err := db.GetCategoryByName(categoryNames[0])
		if err != nil {
			return []db.Post{}, err
		}
		return db.GetPostsByCategory(category.CatID)
	}

	// Multiple categories - collect all posts and remove duplicates
	postMap := make(map[int]db.Post)

	for _, categoryName := range categoryNames {
		category, err := db.GetCategoryByName(categoryName)
		if err != nil {
			continue // Skip invalid categories
		}

		posts, err := db.GetPostsByCategory(category.CatID)
		if err != nil {
			continue
		}

		// Add posts to map to avoid duplicates
		for _, post := range posts {
			postMap[post.PostID] = post
		}
	}

	// Convert map back to slice
	var allPosts []db.Post
	for _, post := range postMap {
		allPosts = append(allPosts, post)
	}

	return allPosts, nil
}

// Helper function to get category icons
func getCategoryIcon(catName string) string {
	iconMap := map[string]string{
		"General":    "fas fa-hashtag",
		"Technology": "fas fa-laptop-code",
		"Gaming":     "fas fa-gamepad",
		"Movies":     "fas fa-film",
		"Music":      "fas fa-music",
		"Sports":     "fas fa-futbol",
		"Food":       "fas fa-utensils",
		"Travel":     "fas fa-plane",
		"Fashion":    "fas fa-tshirt",
		"Science":    "fas fa-atom",
	}

	if icon, exists := iconMap[catName]; exists {
		return icon
	}
	return "fas fa-hashtag" // default icon
}
