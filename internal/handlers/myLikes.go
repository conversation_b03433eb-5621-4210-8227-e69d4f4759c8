package handlers

import (
	"forum/internal/cookie"
	"forum/internal/db"
	"forum/internal/errors"
	Renderer "forum/internal/server/renderer"
	"net/http"
)

func MyLikesHandler(w http.ResponseWriter, r *http.Request) {
	if !cookie.IsAuthenticated(r) {
		cookie.DeleteCookie(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	if r.Method != http.MethodGet {
		errors.MethodNotAllowedHandler(w, r)
		return
	}

	userID, err := db.GetUserID(r)
	if err != nil {
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	likes, err := db.GetLikesByUser(userID)
	if err != nil {
		errors.InternalErrorHandler(w, r, err)
		return
	}

	data := struct {
		Template        string
		IsAuthenticated bool
		Likes           []db.Like
	}{
		Template:        "myLikes",
		IsAuthenticated: cookie.IsAuthenticated(r),
		Likes:           likes,
	}

	Renderer.RenderTemplate(w, "layout", data)

}
