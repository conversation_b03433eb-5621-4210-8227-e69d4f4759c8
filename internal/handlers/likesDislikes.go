package handlers

import (
	"forum/internal/cookie"
	"forum/internal/db"
	"forum/internal/errors"
	"net/http"
	"strconv"
	"strings"
)

// LikePostHandler handles liking/disliking posts
func LikePostHandler(w http.ResponseWriter, r *http.Request) {
	if !cookie.IsAuthenticated(r) {
		cookie.DeleteCookie(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	if r.Method != http.MethodPost {
		errors.MethodNotAllowedHandler(w, r)
		return
	}

	userID, err := db.GetUserID(r)
	if err != nil {
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	err = r.ParseForm()
	if err != nil {
		errors.BadRequestHandler(w, r, "Error parsing form data")
		return
	}

	postIDStr := r.FormValue("postID")
	valueStr := r.FormValue("value") // "1" for like, "-1" for dislike
	redirectURL := r.FormValue("redirect")

	// Validate inputs
	postID, err := strconv.Atoi(postIDStr)
	if err != nil {
		errors.BadRequestHandler(w, r, "Invalid post ID")
		return
	}

	value, err := strconv.Atoi(valueStr)
	if err != nil || (value != 1 && value != -1) {
		errors.BadRequestHandler(w, r, "Invalid like value")
		return
	}

	if !db.PostExists(postID) {
		errors.NotFoundHandler(w, r)
		return
	}

	err = db.LikePost(userID, postID, value)
	if err != nil {
		errors.InternalErrorHandler(w, r, err)
		return
	}

	if redirectURL != "/myposts" && redirectURL != "/mylikes" && strings.HasSuffix(redirectURL, "/comments") {
		redirectURL = "/home"
	}
	http.Redirect(w, r, redirectURL, http.StatusSeeOther)
}

func LikeCommentHandler(w http.ResponseWriter, r *http.Request) {
	if !cookie.IsAuthenticated(r) {
		cookie.DeleteCookie(w, r)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	if r.Method != http.MethodPost {
		errors.MethodNotAllowedHandler(w, r)
		return
	}

	userID, err := db.GetUserID(r)
	if err != nil {
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	err = r.ParseForm()
	if err != nil {
		errors.BadRequestHandler(w, r, "Error parsing form data")
		return
	}

	commentIDStr := r.FormValue("commentID")
	valueStr := r.FormValue("value") // "1" for like, "-1" for dislike

	// Validate inputs
	commentID, err := strconv.Atoi(commentIDStr)
	if err != nil {
		errors.BadRequestHandler(w, r, "Invalid comment ID")
		return
	}

	value, err := strconv.Atoi(valueStr)
	if err != nil || (value != 1 && value != -1) {
		errors.BadRequestHandler(w, r, "Invalid like value")
		return
	}

	comment, err := db.GetCommentByID(commentID)

	if err != nil {
		errors.NotFoundHandler(w, r)
		return
	}

	err = db.LikeComment(userID, commentID, value)
	if err != nil {
		errors.InternalErrorHandler(w, r, err)
		return
	}
	http.Redirect(w, r, "/comments?postid="+strconv.Itoa(comment.PostID), http.StatusSeeOther)
}
