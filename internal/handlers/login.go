package handlers

import (
	"forum/internal/db"
	"forum/internal/errors"
	Renderer "forum/internal/server/renderer"
	"net/http"
)

func LoginHandler(w http.ResponseWriter, r *http.Request) {
	
	if r.Method == http.MethodGet {
		Renderer.RenderTemplate(w, "log-sign.html", nil)
		_, err := db.GetUserID(r)
		if err == nil {
			http.Redirect(w, r, "/home", http.StatusSeeOther)
			return
		}
		return
	} else if r.Method == http.MethodPost {

		// check if user is logged in
		_, err := db.GetUserID(r)
		if err == nil {
			http.Redirect(w, r, "/home", http.StatusSeeOther)
			return
		}

		// Parse form data
		err = r.ParseForm()
		if err != nil {
			errors.BadRequestHandler(w, r, "Error parsing form data")
			return
		}

		// Get form values
		email := r.FormValue("email")
		password := r.FormValue("password")

		// Basic validation
		if email == "" || password == "" {
			errors.BadRequestHandler(w, r, "All fields are required")
			return
		}

		// Check user status
		if !db.UserExists(email) {
			errors.UnauthorizedHandler(w, r)
			return
		}

		if !db.CheckPass(email, password) {
			errors.UnauthorizedHandler(w, r)
			return
		}

		err = db.DeletePastSessions(email)
		if err != nil {
			errors.InternalErrorHandler(w, r, err)
			return
		}

		cookie, err := db.AddSession(email)
		if err != nil {
			errors.InternalErrorHandler(w, r, err)
			return
		}

		http.SetCookie(w, &cookie)

		http.Redirect(w, r, "/home", http.StatusSeeOther)

	}
}
