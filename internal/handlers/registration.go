package handlers

import (
	"forum/internal/db"
	Renderer "forum/internal/server/renderer"
	"net/http"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// RegisterHandler handles user registration
func RegisterHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == http.MethodGet {
		Renderer.RenderTemplate(w, "signup.html", nil)
		return
	} else if r.Method == http.MethodPost {

		// check if user is logged in
		_, err := db.GetUserID(r)
		if err == nil {
			http.Redirect(w, r, "/home", http.StatusSeeOther)
			return
		}

		// Parse form data
		err = r.ParseForm()
		if err != nil {
			http.Error(w, "Error parsing form data", http.StatusBadRequest)
			return
		}

		// Get form values
		email := r.FormValue("email")
		username := r.FormValue("username")
		password := r.FormValue("password")

		// Basic validation
		if email == "" || username == "" || password == "" {
			http.Error(w, "All fields are required", http.StatusBadRequest)
			return
		}

		// Hash the password
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		if err != nil {
			http.Error(w, "Error hashing password", http.StatusInternalServerError)
			return
		}

		// Create new user
		user := &db.User{
			Username:  username,
			Email:     email,
			Password:  string(hashedPassword),
			CreatedAt: time.Now(),
		}

		// Insert user into database
		err = user.InsertUser()
		if err != nil {
			// Check for duplicate email/username
			if err.Error() == "UNIQUE constraint failed: User.email" {
				http.Error(w, "Email already in use", http.StatusConflict)
				return
			}
			if err.Error() == "UNIQUE constraint failed: User.username" {
				http.Error(w, "Username already taken", http.StatusConflict)
				return
			}
			http.Error(w, "Error creating user: "+err.Error(), http.StatusInternalServerError)
			return
		}

		// give user session token
		cookie, err := db.AddSession(email)
		if err != nil {
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}

		http.SetCookie(w, &cookie)

		// Registration successful
		http.Redirect(w, r, "/home", http.StatusMovedPermanently)
	}
}
