package website

import (
	"fmt"
	"forum/internal/cookie"

	"forum/internal/db"
	Renderer "forum/internal/server/renderer"
	"net/http"
	"strconv"
	"time"
)

func CommentsHandler(w http.ResponseWriter, r *http.Request) {

	if r.Method == http.MethodGet {
		postIDStr := r.FormValue("postid")
		postID, err := strconv.Atoi(postIDStr)
		if err != nil {
			http.Error(w, "Invalid post ID", http.StatusBadRequest)
			fmt.Println("post ID at GET request : ", postID)
			fmt.Println("the error is ", err)
			return
		}

		fmt.Println("postID:", postID)

		postObject, err := db.GetPostByID(postID) //of type struct of db.Post
		if err != nil {
			http.Error(w, "Error finding the post", http.StatusInternalServerError)
			fmt.Println("error finding the post: ", err)
			return
		}

		categories, err := db.GetAllCategories()
		if err != nil {
			http.Error(w, "Failed to fetch categories: "+err.Error(), http.StatusInternalServerError)
			return
		}

		comments, err := db.GetCommentsByPost(postID)
		if err != nil {
			http.Error(w, "Failed to fetch comments: "+err.Error(), http.StatusInternalServerError)
			return
		}

		data := struct {
			Template        string
			IsAuthenticated bool
			Posts           []db.Post
			Categories      []db.Category
			Comments        []db.Comment
		}{
			Template:        "comments",
			IsAuthenticated: cookie.IsAuthenticated(r),
			Posts:           []db.Post{postObject}, // wrap in slice to avoid conflicts
			Categories:      categories,
			Comments:        comments,
		}

		Renderer.RenderTemplate(w, "layout", data)

	} else if r.Method == http.MethodPost {

		if !cookie.IsAuthenticated(r) {
			cookie.DeleteCookie(w, r)
			http.Redirect(w, r, "/login", http.StatusSeeOther)
			return
		}

		commentContent := r.FormValue("content")
		createdAt := time.Now()
		userID, err := db.GetUserID(r)
		if err != nil {

			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		postIDStr := r.FormValue("postid")
		postID, err := strconv.Atoi(postIDStr)
		if err != nil {
			http.Error(w, err.Error(), http.StatusBadRequest)
			fmt.Println("the error is ", err)
			return
		}

		fmt.Println("post ID at post request : ", postID)

		comment := db.Comment{
			CreatedAt: createdAt,
			PostID:    postID,
			UserID:    userID,
			Content:   commentContent,
		}

		insertError := comment.InsertComment()
		if insertError != nil {
			http.Error(w, "Error inserting comment", http.StatusInternalServerError)
			return
		}

		fmt.Println("Comment inserted successfully")

		http.Redirect(w, r, "/comments?postid="+strconv.Itoa(postID), http.StatusSeeOther)

	}

}
