package website

import (
	"fmt"
	 "forum/internal/cookie"
	"forum/internal/db"
	Renderer "forum/internal/server/renderer"
	"net/http"
)

func Posts(w http.ResponseWriter, r *http.Request) {
	if !cookie.IsAuthenticated(r){
			
		cookie.DeleteCookie(w, r )
		http.Redirect(w, r, "/home", http.StatusSeeOther)
		return
	}else{
		fmt.Println("tries to execute the else block in /myposts")
		 userID, err := db.GetUserID(r)
	// fmt.Println("user id : ", userID)
	if err != nil {
		http.Error(w, "Failed to fetch user", http.StatusInternalServerError)
		fmt.Println(err)
		return
	}

	posts, err := db.GetPostsByID(userID)
	if err != nil {
		http.Error(w, "Failed to fetch posts", http.StatusInternalServerError)
		return
	}
	//  fmt.Println(" posts : ", posts)

	categories, err := db.GetAllCategories()
	if err != nil {
		http.Error(w, "Failed to fetch categories: "+err.Error(), http.StatusInternalServerError)

	}

	data := struct {
		Template        string
		IsAuthenticated bool
		Posts           []db.Post
		Categories      []db.Category
	}{
		Template:        "myposts",
		IsAuthenticated: cookie.IsAuthenticated(r),
		Posts:           posts,
		Categories:      categories,
	}
	Renderer.RenderTemplate(w, "layout", data)
	}
	
}
