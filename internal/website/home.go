package website

import (
	"forum/internal/cookie"
	"forum/internal/db"
	Renderer "forum/internal/server/renderer"

	//	"log"
	"net/http"
	//"fmt"
)



func Home(w http.ResponseWriter, r *http.Request) {

	posts, err := db.GetAllPosts()
	if err != nil {
		http.Error(w, "Failed to fetch posts: "+err.Error(), http.StatusInternalServerError)
		return
	}

	    // Define category icons mapping
    categoryIcons := map[string]string{
        "General":    "fas fa-hashtag",
        "Technology": "fas fa-laptop-code",
        "Gaming":     "fas fa-gamepad",
        "Movies":     "fas fa-film",
        "Music":      "fas fa-music",
        "Sports":     "fas fa-futbol",
        "Food":       "fas fa-utensils",
        "Travel":     "fas fa-plane",
        "Fashion":    "fas fa-tshirt",
        "Science":    "fas fa-atom",
    }

	categories, err := db.GetAllCategories()
	if err != nil {
		http.Error(w, "Failed to fetch categories: "+err.Error(), http.StatusInternalServerError)

	}

	   for i := range categories {
        if icon, exists := categoryIcons[categories[i].CatName]; exists {
            categories[i].Icon = icon
        }
    }

	data := struct {
		Template        string
		IsAuthenticated bool
		Posts           []db.Post
		Categories      []db.Category
	}{
		Template:        "index",
		IsAuthenticated: cookie.IsAuthenticated(r),
		Posts:           posts,
		Categories:      categories,
	}

	Renderer.RenderTemplate(w, "layout", data)
}
