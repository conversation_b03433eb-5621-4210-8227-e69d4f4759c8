package Renderer

import (
	"fmt"
	"html/template"
	"net/http"
)

var templates = template.Must(template.ParseGlob("static/templates/*.html"))

func RenderTemplate(w http.ResponseWriter, name string, data interface{}) {
	// Log the data being passed to the template for debugging
	// fmt.Printf("Rendering template '%s' with data: %+v\n", name, data)

	err := templates.ExecuteTemplate(w, name, data)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		fmt.Println("error is : ", err)
		return
	}
}
