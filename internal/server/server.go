package server

import (
	"fmt"
	"forum/internal/errors"
	"forum/internal/handlers"
	"forum/internal/website"
	"log"
	"net/http"
	"time"
)

// notFoundHandler handles 404 errors
func notFoundHandler() http.Handler {
	return http.HandlerFunc(errors.NotFoundHandler)
}

func Server() {
	mux := http.NewServeMux()

	// Register routes
	mux.HandleFunc("/home", website.Home)                 // Home route handler
	mux.HandleFunc("/myposts", website.Posts)             // for retrieving user posts
	mux.HandleFunc("/post", handlers.PostHandler)         // for posting
	mux.HandleFunc("/login", handlers.LoginHandler)       // Login route handler
	mux.HandleFunc("/register", handlers.RegisterHandler) // signup route handler
	mux.HandleFunc("/logout", handlers.LogoutHandler)     // logout router handler
	mux.HandleFunc("/", handlers.Root)
	mux.HandleFunc("/comments", website.CommentsHandler)
	mux.HandleFunc("/like", handlers.LikePostHandler)
	mux.HandleFunc("/mylikes", website.MyLikes)
	mux.HandleFunc("/filter", handlers.FilterHandler)

	// Serve static files with proper caching headers
	fs := http.FileServer(http.Dir("static"))
	mux.Handle("/static/", http.StripPrefix("/static/", fs))

	// Create a custom 404 handler with logging
	handler := errors.LoggingMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// If no route matches, serve 404
		switch r.URL.Path {
		case "/", "/home", "/myposts", "/post", "/login", "/register", "/logout", "/comments", "/like", "/mylikes", "/filter":
			mux.ServeHTTP(w, r)
		default:
			if len(r.URL.Path) <= 8 || r.URL.Path[:8] != "/static/" {
				notFoundHandler().ServeHTTP(w, r)
			} else {
				mux.ServeHTTP(w, r)
			}
		}
	}))

	server := &http.Server{
		Addr:         "0.0.0.0:8080",
		Handler:      handler,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	fmt.Println("Server running at http://localhost:8080")
	log.Fatal(server.ListenAndServe())
}
