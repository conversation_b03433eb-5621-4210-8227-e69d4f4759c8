package cookie

import (
	"net/http"
	"time"
	"fmt"
	"forum/internal/db"
)

func IsAuthenticated(r *http.Request) bool {
	cookie, err := r.<PERSON>("sessionToken")
	if err != nil {
		fmt.Println("error in authenticate.go is:", err)
		return false
	}

	// check if the cookie has expired
	if !db.ValidSession(cookie.Value){
		return false
	}
	return true
}

func DeleteCookie(w http.ResponseWriter, r *http.Request) {
	cookie, err := r.<PERSON>("sessionToken")
	if err != nil {
		return
	}

	cookie = &http.Cookie{
		Name:     "sessionToken",
		Value:    "",
		MaxAge:   -1,
		Expires:  time.Now().Add(-1 * time.Hour),
		HttpOnly: true,
	}
	http.SetCookie(w, cookie)

}
