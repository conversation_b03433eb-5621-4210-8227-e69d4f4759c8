package db

import (
	"database/sql"
	"time"
)

type Like struct {
	LikeID    int
	PostID    *int // Nullable - either PostID or CommentID will be set
	CommentID *int // Nullable - either PostID or CommentID will be set
	UserID    int
	Value     int // 1 = like, -1 = dislike
	CreatedAt time.Time
}

// LikePost adds or updates a like/dislike on a post
func LikePost(userID, postID, value int) error {
	// Check if user has already liked/disliked this post
	existingLike, err := getUserPostLike(userID, postID)
	if err != nil && err != sql.ErrNoRows {
		return err
	}

	if existingLike != nil {
		// Remove existing like first
		err = removeLike(existingLike.LikeID)
		if err != nil {
			return err
		}

		// If same value, just remove (toggle off)
		if existingLike.Value == value {
			return nil
		}
		// If different value, fall through to create new like
	}

	// Create new like
	return insertPostLike(userID, postID, value)
}

// LikeComment adds or updates a like/dislike on a comment
func LikeComment(userID, commentID, value int) error {
	// Check if user has already liked/disliked this comment
	existingLike, err := getUserCommentLike(userID, commentID)
	if err != nil && err != sql.ErrNoRows {
		return err
	}

	if existingLike != nil {
		// Remove existing like first
		err = removeLike(existingLike.LikeID)
		if err != nil {
			return err
		}

		// If same value, just remove (toggle off)
		if existingLike.Value == value {
			return nil
		}
		// If different value, fall through to create new like
	}

	// Create new like
	return insertCommentLike(userID, commentID, value)
}

// GetPostLikes returns the like and dislike counts for a post
func GetPostLikes(postID int) (likes int, dislikes int, err error) {
	query := `
		SELECT 
			COALESCE(SUM(CASE WHEN value = 1 THEN 1 ELSE 0 END), 0) as likes,
			COALESCE(SUM(CASE WHEN value = -1 THEN 1 ELSE 0 END), 0) as dislikes
		FROM Likes 
		WHERE postID = ?`

	err = DB.QueryRow(query, postID).Scan(&likes, &dislikes)
	return likes, dislikes, err
}

// GetCommentLikes returns the like and dislike counts for a comment
func GetCommentLikes(commentID int) (likes int, dislikes int, err error) {
	query := `
		SELECT 
			COALESCE(SUM(CASE WHEN value = 1 THEN 1 ELSE 0 END), 0) as likes,
			COALESCE(SUM(CASE WHEN value = -1 THEN 1 ELSE 0 END), 0) as dislikes
		FROM Likes 
		WHERE commentID = ?`

	err = DB.QueryRow(query, commentID).Scan(&likes, &dislikes)
	return likes, dislikes, err
}

// GetUserPostLike returns the user's like status for a post (1, -1, or 0 if no like)
func GetUserPostLike(userID, postID int) (int, error) {
	like, err := getUserPostLike(userID, postID)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, nil // No like found
		}
		return 0, err
	}
	return like.Value, nil
}

// GetUserCommentLike returns the user's like status for a comment (1, -1, or 0 if no like)
func GetUserCommentLike(userID, commentID int) (int, error) {
	like, err := getUserCommentLike(userID, commentID)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, nil // No like found
		}
		return 0, err
	}
	return like.Value, nil
}

// Helper functions

func getUserPostLike(userID, postID int) (*Like, error) {
	query := `SELECT likeID, postID, userID, value, createdAt FROM Likes WHERE userID = ? AND postID = ?`

	var like Like
	var postIDVal sql.NullInt64

	err := DB.QueryRow(query, userID, postID).Scan(
		&like.LikeID, &postIDVal, &like.UserID, &like.Value, &like.CreatedAt,
	)

	if err != nil {
		return nil, err
	}

	if postIDVal.Valid {
		postID := int(postIDVal.Int64)
		like.PostID = &postID
	}

	return &like, nil
}

func getUserCommentLike(userID, commentID int) (*Like, error) {
	query := `SELECT likeID, commentID, userID, value, createdAt FROM Likes WHERE userID = ? AND commentID = ?`

	var like Like
	var commentIDVal sql.NullInt64

	err := DB.QueryRow(query, userID, commentID).Scan(
		&like.LikeID, &commentIDVal, &like.UserID, &like.Value, &like.CreatedAt,
	)

	if err != nil {
		return nil, err
	}

	if commentIDVal.Valid {
		commentID := int(commentIDVal.Int64)
		like.CommentID = &commentID
	}

	return &like, nil
}

func insertPostLike(userID, postID, value int) error {
	query := `INSERT INTO Likes (postID, userID, value) VALUES (?, ?, ?)`
	_, err := DB.Exec(query, postID, userID, value)
	return err
}

func insertCommentLike(userID, commentID, value int) error {
	query := `INSERT INTO Likes (commentID, userID, value) VALUES (?, ?, ?)`
	_, err := DB.Exec(query, commentID, userID, value)
	return err
}

func removeLike(likeID int) error {
	query := `DELETE FROM Likes WHERE likeID = ?`
	_, err := DB.Exec(query, likeID)
	return err
}

func GetLikesByUser(userID int) ([]Like, error) {
	query := `SELECT likeID, postID, commentID, userID, value, createdAt FROM Likes WHERE userID = ?`
	rows, err := DB.Query(query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var likes []Like
	for rows.Next() {
		var like Like
		var postIDVal, commentIDVal sql.NullInt64

		err := rows.Scan(
			&like.LikeID, &postIDVal, &commentIDVal, &like.UserID, &like.Value, &like.CreatedAt,
		)
		if err != nil {
			return nil, err
		}

		if postIDVal.Valid {
			postID := int(postIDVal.Int64)
			like.PostID = &postID
		}
		if commentIDVal.Valid {
			commentID := int(commentIDVal.Int64)
			like.CommentID = &commentID
		}

		likes = append(likes, like)
	}

	return likes, rows.Err()
}

// GetLikedPostsByUser retrieves all posts that a user has liked (only likes, not dislikes)
func GetLikedPostsByUser(userID int) ([]Post, error) {
	query := `
		SELECT p.postID, p.userID, p.date, p.title, p.content, u.username,
		COALESCE(likes.like_count, 0) as like_count,
		COALESCE(dislikes.dislike_count, 0) as dislike_count
		FROM Post p
		JOIN User u ON p.userID = u.userID
		JOIN Likes l ON p.postID = l.postID
		LEFT JOIN (
			SELECT postID, COUNT(*) as like_count
			FROM Likes
			WHERE value = 1 AND postID IS NOT NULL
			GROUP BY postID
		) likes ON p.postID = likes.postID
		LEFT JOIN (
			SELECT postID, COUNT(*) as dislike_count
			FROM Likes
			WHERE value = -1 AND postID IS NOT NULL
			GROUP BY postID
		) dislikes ON p.postID = dislikes.postID
		WHERE l.userID = ? AND l.value = 1
		ORDER BY l.createdAt DESC`

	rows, err := DB.Query(query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var posts []Post
	for rows.Next() {
		var post Post
		err := rows.Scan(
			&post.PostID, &post.UserID, &post.Date,
			&post.Title, &post.Content, &post.Username,
			&post.Likes, &post.Dislikes,
		)
		if err != nil {
			return nil, err
		}

		// Get categories for this post
		categories, err := GetPostCategories(post.PostID)
		if err != nil {
			return nil, err
		}
		post.Categories = categories

		posts = append(posts, post)
	}

	return posts, rows.Err()
}
