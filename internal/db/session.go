package db

import (
	"errors"
	Auth "forum/internal/auth"
	"net/http"
	"time"
)

func AddSession(email string) (http.Cookie, error) {

	var userID int
	err := DB.QueryRow("SELECT userID FROM User WHERE email = ?",
		email).Scan(&userID)
	if err != nil {
		return http.Cookie{}, err
	}

	value, err := Auth.GenerateToken()
	expires := time.Now().Add(7 * 24 * time.Hour)

	if err != nil {
		return http.Cookie{}, err
	}

	_, err = DB.Exec("INSERT INTO sessions (sessionID, userID, valid, expiresAt) VALUES (?, ?, ?, ?)",
		value, userID, true, expires)
	if err != nil {
		return http.Cookie{}, errors.New("database insert error")
	}

	cookie := http.Cookie{
		Name:     "sessionToken",
		Value:    value,
		Expires:  expires,
		HttpOnly: true,
	}

	return cookie, nil
}

func DeletePastSessions(email string) error {
	var userID int
	err := DB.QueryRow("SELECT userID FROM User WHERE email = ?",
		email).Scan(&userID)
	if err != nil {
		return err
	}
	_, err = DB.Exec("UPDATE sessions SET valid = false WHERE userID = ?",
		userID)
	if err != nil {
		return err
	}
	return nil
}

func ValidSession(value string) bool {

	var expirey time.Time
	var valid bool
	err := DB.QueryRow("SELECT expiresAt, valid FROM sessions WHERE sessionID = ?",
		value).Scan(&expirey, &valid)
	if err != nil {
		return false
	}

	return time.Now().Before(expirey) && valid
}
