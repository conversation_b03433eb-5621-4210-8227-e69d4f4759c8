package db

import (
	"errors"
	//"forum/internal/auth"
	"net/http"
	"time"

	"golang.org/x/crypto/bcrypt"
)

type User struct {
	UserID    int
	Username  string
	Password  string
	Email     string
	CreatedAt time.Time
	UserImage string
}

func (u *User) InsertUser() error {
	query := `INSERT INTO User (username, email, password, userImage) VALUES (?, ?, ?, ?)`

	res, err := DB.Exec(query, u.Username, u.Email, u.Password, u.UserImage)
	if err != nil {
		return err
	}

	id, err := res.LastInsertId()
	if err != nil {
		return err
	}
	u.UserID = int(id)
	return nil
}

func UserExists(email string) bool {
	var exists int
	err := DB.QueryRow("SELECT 1 FROM User WHERE email = ?", email).Scan(&exists)
	return err == nil
}

func CheckPass(email, password string) bool {
	var hashedPassword string

	err := DB.QueryRow("SELECT password FROM User WHERE email = ?", email).Scan(&hashedPassword)
	if err != nil {
		return false
	}

	err = bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}

func GetUserID(r *http.Request) (int, error) {
	cookie, err := r.Cookie("sessionToken")
	if err != nil {
		return -1, err
	}

	var userID int
	err = DB.QueryRow("SELECT userID FROM sessions WHERE sessionID = ?", cookie.Value).Scan(&userID)
	if err != nil {
		return -1, err
	}

	if ValidSession(cookie.Value) {
		return userID, nil
	} else {
		return -2, errors.New("invalid session")
	}
}

func Logout(userID int) error {
	_, err := DB.Exec("UPDATE sessions SET valid = false WHERE userID = ?",
		userID)
	return err
}

func GetUserName(userID int) (string, error) {
	var username string
	err := DB.QueryRow("SELECT username FROM User WHERE userID = ?", userID).Scan(&username)
	return username, err
}
