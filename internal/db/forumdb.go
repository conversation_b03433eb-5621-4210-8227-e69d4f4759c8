package db

import (
	"database/sql"
	"log"
	"os"

	_ "github.com/mattn/go-sqlite3"
)

var DB *sql.DB
var name string

func Init(schemaFile string) {
	name = "forum.db"
	var err error
	DB, err = sql.Open("sqlite3", name)
	if err != nil {
		log.Fatal("DB ERROR", err)
	}
	if err = DB.Ping(); err != nil {
		log.Fatal("DB ERROR", err)
	}
	log.Println("Connected to database")

	if schemaFile != "" {
		schema, err := os.ReadFile(schemaFile)
		if err != nil {
			log.Fatal("Cannot read schema file:", err)
		}
		_, err = DB.Exec(string(schema))
		if err != nil {
			log.Fatal("Cannot execute schema:", err)
		}
	}
}

func DbClose() {
	if DB != nil {
		DB.Close()
	}
}

func DBname() string {
	return name
}
