package db

import (
	"time"
)

type Comment struct {
	CommentID int
	CreatedAt time.Time
	PostID    int
	UserID    int
	Content   string
	Username  string
	Likes     int
	Dislikes  int
}

// InsertComment adds a new comment to the database
func (c *Comment) InsertComment() error {
	query := `INSERT INTO Comments (postID, userID, content) VALUES (?, ?, ?)`

	res, err := DB.Exec(query, c.PostID, c.UserID, c.Content)
	if err != nil {
		return err
	}

	id, err := res.LastInsertId()
	if err != nil {
		return err
	}
	c.CommentID = int(id)
	return nil
}

// GetCommentsByPost retrieves all comments for a specific post with like/dislike counts
func GetCommentsByPost(postID int) ([]Comment, error) {
	query := `
		SELECT c.commentID, c.createdAt, c.postID, c.userID, c.content, u.username,
		       COALESCE(SUM(CASE WHEN l.value = 1 THEN 1 ELSE 0 END), 0) as likes,
		       COALESCE(SUM(CASE WHEN l.value = -1 THEN 1 ELSE 0 END), 0) as dislikes
		FROM Comments c
		JOIN User u ON c.userID = u.userID
		LEFT JOIN Likes l ON c.commentID = l.commentID
		WHERE c.postID = ?
		GROUP BY c.commentID, c.createdAt, c.postID, c.userID, c.content, u.username
		ORDER BY c.createdAt ASC`

	rows, err := DB.Query(query, postID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var comments []Comment
	for rows.Next() {
		var comment Comment
		err := rows.Scan(
			&comment.CommentID, &comment.CreatedAt, &comment.PostID,
			&comment.UserID, &comment.Content, &comment.Username,
			&comment.Likes, &comment.Dislikes,
		)
		if err != nil {
			return nil, err
		}
		comments = append(comments, comment)
	}

	return comments, rows.Err()
}

// GetCommentByID retrieves a specific comment by its ID
func GetCommentByID(commentID int) (*Comment, error) {
	query := `
		SELECT c.commentID, c.createdAt, c.postID, c.userID, c.content, u.username,
		       COALESCE(SUM(CASE WHEN l.value = 1 THEN 1 ELSE 0 END), 0) as likes,
		       COALESCE(SUM(CASE WHEN l.value = -1 THEN 1 ELSE 0 END), 0) as dislikes
		FROM Comments c
		JOIN User u ON c.userID = u.userID
		LEFT JOIN Likes l ON c.commentID = l.commentID
		WHERE c.commentID = ?
		GROUP BY c.commentID, c.createdAt, c.postID, c.userID, c.content, u.username`

	var comment Comment
	err := DB.QueryRow(query, commentID).Scan(
		&comment.CommentID, &comment.CreatedAt, &comment.PostID,
		&comment.UserID, &comment.Content, &comment.Username,
		&comment.Likes, &comment.Dislikes,
	)
	if err != nil {
		return nil, err
	}

	return &comment, nil
}

// GetCommentsByUser retrieves all comments by a specific user
func GetCommentsByUser(userID int) ([]Comment, error) {
	query := `
		SELECT c.commentID, c.createdAt, c.postID, c.userID, c.content, u.username,
		       COALESCE(SUM(CASE WHEN l.value = 1 THEN 1 ELSE 0 END), 0) as likes,
		       COALESCE(SUM(CASE WHEN l.value = -1 THEN 1 ELSE 0 END), 0) as dislikes
		FROM Comments c
		JOIN User u ON c.userID = u.userID
		LEFT JOIN Likes l ON c.commentID = l.commentID
		WHERE c.userID = ?
		GROUP BY c.commentID, c.createdAt, c.postID, c.userID, c.content, u.username
		ORDER BY c.createdAt DESC`

	rows, err := DB.Query(query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var comments []Comment
	for rows.Next() {
		var comment Comment
		err := rows.Scan(
			&comment.CommentID, &comment.CreatedAt, &comment.PostID,
			&comment.UserID, &comment.Content, &comment.Username,
			&comment.Likes, &comment.Dislikes,
		)
		if err != nil {
			return nil, err
		}
		comments = append(comments, comment)
	}

	return comments, rows.Err()
}

// DeleteComment removes a comment from the database
func DeleteComment(commentID int) error {
	// Due to CASCADE constraints, this will also delete associated likes
	query := `DELETE FROM Comments WHERE commentID = ?`
	_, err := DB.Exec(query, commentID)
	return err
}

// GetCommentCount returns the total number of comments for a post
func GetCommentCount(postID int) (int, error) {
	query := `SELECT COUNT(*) FROM Comments WHERE postID = ?`

	var count int
	err := DB.QueryRow(query, postID).Scan(&count)
	if err != nil {
		return 0, err
	}

	return count, nil
}
