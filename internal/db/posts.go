package db

import (
	"fmt"
	"time"
)

type Post struct {
	PostID     int
	UserID     int
	Date       time.Time
	Title      string
	Content    string
	Username   string
	Categories []string
	Likes      int
	Dislikes   int
}

func (p *Post) InsertPost(catgegoryNames []string) error {
	query := `INSERT INTO Post (userID, title, content) VALUES (?, ?, ?)`
	fmt.Println("Executing query:", query)
	fmt.Println("With values:", p.UserID, p.Title, p.Content)

	res, err := DB.Exec(query, p.UserID, p.Title, p.Content)
	if err != nil {
		fmt.Println("DB Exec error:", err)
		return err
	}

	id, err := res.LastInsertId()
	if err != nil {
		fmt.Println("LastInsertId error:", err)
		return err
	}

	p.PostID = int(id)

	var categoryIDs []int
	for _, catName := range catgegoryNames {
		Cat, err := GetCategoryByName(catName)
		if err != nil || Cat == nil {
			fmt.Println("Error fetching CategoryID for", catName, ":", err)
			categoryIDs = append(categoryIDs, 1)
			continue
		}
		categoryIDs = append(categoryIDs, Cat.CatID)
	}

	categoryIDs = removeDuplicateInt(categoryIDs)
	if len(categoryIDs) == 0 {
		categoryIDs = append(categoryIDs, 1)
	}

	for _, categoryID := range categoryIDs {
		queryForPostCat := `INSERT INTO Post_Cat (postID, catID) VALUES (?, ?)`
		_, insertError := DB.Exec(queryForPostCat, p.PostID, categoryID)
		if insertError != nil {
			fmt.Println("DB Exec error:", insertError)
			return insertError
		}
	}

	fmt.Println("Post inserted with ID:", p.PostID)
	fmt.Println("Junction inserted.")

	return nil
}

func GetPostsByID(userID int) ([]Post, error) {
	query := `
		SELECT p.postID, p.userID, p.date, p.title, p.content, u.username,
		COALESCE(likes.like_count, 0) as like_count,
		COALESCE(dislikes.dislike_count, 0) as dislike_count
		FROM Post p
		JOIN User u ON p.userID = u.userID
		LEFT JOIN (
			SELECT postID, COUNT(*) as like_count 
			FROM Likes 
			WHERE value = 1 AND postID IS NOT NULL 
			GROUP BY postID
		) likes ON p.postID = likes.postID
		LEFT JOIN (
			SELECT postID, COUNT(*) as dislike_count 
			FROM Likes 
			WHERE value = -1 AND postID IS NOT NULL 
			GROUP BY postID
		) dislikes ON p.postID = dislikes.postID
		WHERE p.userID = ?
		ORDER BY p.date DESC`

	rows, err := DB.Query(query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var posts []Post
	for rows.Next() {
		var post Post
		err := rows.Scan(
			&post.PostID, &post.UserID, &post.Date,
			&post.Title, &post.Content, &post.Username,
			&post.Likes, &post.Dislikes,
		)
		if err != nil {
			return nil, err
		}

		// get categories for this post
		categories, err := GetPostCategories(post.PostID)
		if err != nil {
			return nil, err
		}
		post.Categories = categories

		posts = append(posts, post)
	}

	return posts, nil
}

// GetAllPosts retrieves all posts with author info, ordered by date (newest first)
func GetAllPosts() ([]Post, error) {
	query := `
		SELECT p.postID, p.userID, p.date, p.title, p.content, u.username,
		COALESCE(likes.like_count, 0) as like_count,
		COALESCE(dislikes.dislike_count, 0) as dislike_count
		FROM Post p
		JOIN User u ON p.userID = u.userID
		LEFT JOIN (
			SELECT postID, COUNT(*) as like_count 
			FROM Likes 
			WHERE value = 1 AND postID IS NOT NULL 
			GROUP BY postID
		) likes ON p.postID = likes.postID
		LEFT JOIN (
			SELECT postID, COUNT(*) as dislike_count 
			FROM Likes 
			WHERE value = -1 AND postID IS NOT NULL 
			GROUP BY postID
		) dislikes ON p.postID = dislikes.postID
		ORDER BY p.date DESC`

	rows, err := DB.Query(query)
	//fmt.Println(rows)
	//fmt.Println(DBname())
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var posts []Post
	for rows.Next() {
		var post Post
		err := rows.Scan(
			&post.PostID, &post.UserID, &post.Date,
			&post.Title, &post.Content, &post.Username,
			&post.Likes, &post.Dislikes,
		)
		//fmt.Println("rows.NEXT: ", err)
		if err != nil {
			return nil, err
		}

		// Get categories for this post
		categories, err := GetPostCategories(post.PostID)
		if err != nil {
			return nil, err
		}
		post.Categories = categories

		posts = append(posts, post)
	}

	//fmt.Println("fetching done")

	return posts, rows.Err()
}

func GetPostsByCategory(categoryID int) ([]Post, error) {
	query := `
		COALESCE(likes.like_count, 0) as like_count,
		COALESCE(dislikes.dislike_count, 0) as dislike_count
		FROM Post p
		JOIN User u ON p.userID = u.userID
		LEFT JOIN (
			SELECT postID, COUNT(*) as like_count 
			FROM Likes 
			WHERE value = 1 AND postID IS NOT NULL 
			GROUP BY postID
		) likes ON p.postID = likes.postID
		LEFT JOIN (
			SELECT postID, COUNT(*) as dislike_count 
			FROM Likes 
			WHERE value = -1 AND postID IS NOT NULL 
			GROUP BY postID
		) dislikes ON p.postID = dislikes.postID
		JOIN Post_Cat pc ON p.postID = pc.postID
		WHERE pc.catID = ?
		ORDER BY p.date DESC`

	rows, err := DB.Query(query, categoryID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var posts []Post
	for rows.Next() {
		var post Post
		err := rows.Scan(
			&post.PostID, &post.UserID, &post.Date,
			&post.Title, &post.Content, &post.Username,
			&post.Likes, &post.Dislikes,
		)
		if err != nil {
			return nil, err
		}

		// Get categories for this post
		categories, err := GetPostCategories(post.PostID)
		if err != nil {
			return nil, err
		}
		post.Categories = categories

		posts = append(posts, post)
	}

	return posts, rows.Err()
}

func (p *Post) UpdatePost() error {
	query := `UPDATE Post SET title = ?, content = ? WHERE postID = ?`
	_, err := DB.Exec(query, p.Title, p.Content, p.PostID)
	return err
}

// DeletePost removes a post and all its associated data
func DeletePost(postID int) error {
	query := `DELETE FROM Post WHERE postID = ?`
	_, err := DB.Exec(query, postID)
	return err
}

func AddPostCategory(postID, categoryID int) error {
	query := `INSERT INTO Post_Cat (postID, catID) VALUES (?, ?)`
	_, err := DB.Exec(query, postID, categoryID)
	return err
}

func GetPostCategories(postID int) ([]string, error) {
	query := `
		SELECT c.catName
		FROM Categories c
		JOIN Post_Cat pc ON c.catID = pc.catID
		WHERE pc.postID = ?`

	rows, err := DB.Query(query, postID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var categories []string
	for rows.Next() {
		var category string
		err := rows.Scan(&category)
		if err != nil {
			return nil, err
		}
		categories = append(categories, category)
	}

	return categories, rows.Err()
}

func GetPostByID(postID int) (Post, error) {
	query := `
		SELECT p.postID, p.userID, p.date, p.title, p.content, u.username,
		COALESCE(likes.like_count, 0) as like_count,
		COALESCE(dislikes.dislike_count, 0) as dislike_count
		FROM Post p
		JOIN User u ON p.userID = u.userID
		LEFT JOIN (
			SELECT postID, COUNT(*) as like_count 
			FROM Likes 
			WHERE value = 1 AND postID IS NOT NULL 
			GROUP BY postID
		) likes ON p.postID = likes.postID
		LEFT JOIN (
			SELECT postID, COUNT(*) as dislike_count 
			FROM Likes 
			WHERE value = -1 AND postID IS NOT NULL 
			GROUP BY postID
		) dislikes ON p.postID = dislikes.postID
		 WHERE p.postID = ?`

	var post Post
	err := DB.QueryRow(query, postID).Scan(
		&post.PostID,
		&post.UserID,
		&post.Date,
		&post.Title,
		&post.Content,
		&post.Username,
		&post.Likes,
		&post.Dislikes,
	)
	if err != nil {
		return Post{}, err
	}

	// fetch categories for this post
	categories, err := GetPostCategories(post.PostID)
	if err != nil {
		return Post{}, err
	}
	post.Categories = categories

	return post, nil
}

func removeDuplicateInt(intSlice []int) []int {
	allKeys := make(map[int]bool)
	list := []int{}
	for _, item := range intSlice {
		if _, value := allKeys[item]; !value {
			allKeys[item] = true
			list = append(list, item)
		}
	}
	return list
}
