package db

type Category struct {
	CatID   int
	CatName string
	Icon    string
}

func (c *Category) InsertCategory() error {
	query := `INSERT INTO Categories (catName) VALUES (?)`

	res, err := DB.Exec(query, c.CatName)
	if err != nil {
		return err
	}

	id, err := res.LastInsertId()
	if err != nil {
		return err
	}
	c.CatID = int(id)
	return nil
}

func GetAllCategories() ([]Category, error) {
	query := `SELECT catID, catName FROM Categories ORDER BY catName ASC`

	rows, err := DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var categories []Category
	for rows.Next() {
		var category Category
		err := rows.Scan(&category.CatID, &category.CatName)
		if err != nil {
			return nil, err
		}
		categories = append(categories, category)
	}

	return categories, rows.Err()
}

func GetCategoryByID(catID int) (*Category, error) {
	query := `SELECT catID, catName FROM Categories WHERE catID = ?`

	var category Category
	err := DB.QueryRow(query, catID).Scan(&category.CatID, &category.CatName)
	if err != nil {
		return nil, err
	}

	return &category, nil
}

func GetCategoryByName(catName string) (*Category, error) {
	query := `SELECT catID, catName FROM Categories WHERE catName = ?`

	var category Category
	err := DB.QueryRow(query, catName).Scan(&category.CatID, &category.CatName)
	if err != nil {
		return nil, err
	}

	return &category, nil
}

func (c *Category) UpdateCategory() error {
	query := `UPDATE Categories SET catName = ? WHERE catID = ?`
	_, err := DB.Exec(query, c.CatName, c.CatID)
	return err
}

func DeleteCategory(catID int) error {
	// Due to CASCADE constraints, this will also delete Post_Cat associations
	query := `DELETE FROM Categories WHERE catID = ?`
	_, err := DB.Exec(query, catID)
	return err
}

func CategoryExists(catName string) (bool, error) {
	query := `SELECT COUNT(*) FROM Categories WHERE catName = ?`

	var count int
	err := DB.QueryRow(query, catName).Scan(&count)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

func GetCategoryPostCount(catID int) (int, error) {
	query := `SELECT COUNT(*) FROM Post_Cat WHERE catID = ?`

	var count int
	err := DB.QueryRow(query, catID).Scan(&count)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func RemovePostCategory(postID, catID int) error {
	query := `DELETE FROM Post_Cat WHERE postID = ? AND catID = ?`
	_, err := DB.Exec(query, postID, catID)
	return err
}

func GetPostCategoryIDs(postID int) ([]int, error) {
	query := `SELECT catID FROM Post_Cat WHERE postID = ?`

	rows, err := DB.Query(query, postID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var categoryIDs []int
	for rows.Next() {
		var catID int
		err := rows.Scan(&catID)
		if err != nil {
			return nil, err
		}
		categoryIDs = append(categoryIDs, catID)
	}

	return categoryIDs, rows.Err()
}

m