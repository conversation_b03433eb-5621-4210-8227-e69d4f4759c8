-- Enable foreign key support
PRAG<PERSON> foreign_keys = ON;

-- =========================
-- Users Table
-- =========================
CREATE TABLE IF NOT EXISTS User (
    userID      INTEGER PRIMARY KEY AUTOINCREMENT,
    username    TEXT NOT NULL UNIQUE,
    email       TEXT NOT NULL UNIQUE,
    password    TEXT NOT NULL, -- bcrypt hash
    createdAt   DATETIME DEFAULT CURRENT_TIMESTAMP,
    userImage   TEXT
);

-- =========================
-- Posts Table
-- =========================
CREATE TABLE IF NOT EXISTS Post (
    postID      INTEGER PRIMARY KEY AUTOINCREMENT,
    userID      INTEGER NOT NULL,
    date        DATETIME DEFAULT CURRENT_TIMESTAMP,
    title       TEXT NOT NULL,
    content     TEXT NOT NULL,
    FOREIGN KEY (userID) REFERENCES User(userID) ON DELETE CASCADE
);

-- =========================
-- Comments Table
-- =========================
CREATE TABLE IF NOT EXISTS Comments (
    commentID   INTEGER PRIMARY KEY AUTOINCREMENT,
    createdAt   DATETIME DEFAULT CURRENT_TIMESTAMP,
    postID      INTEGER NOT NULL,
    userID      INTEGER NOT NULL,
    content     TEXT NOT NULL,
    FOREIGN KEY (postID) REFERENCES Post(postID) ON DELETE CASCADE,
    FOREIGN KEY (userID) REFERENCES User(userID) ON DELETE CASCADE
);

-- =========================
-- Categories Table
-- =========================
CREATE TABLE IF NOT EXISTS Categories (
    catID       INTEGER PRIMARY KEY AUTOINCREMENT,
    catName     TEXT NOT NULL UNIQUE
);

-- =========================
-- Post-Categories (Many-to-Many)
-- =========================
CREATE TABLE IF NOT EXISTS Post_Cat (
    postID      INTEGER NOT NULL,
    catID       INTEGER NOT NULL,
    PRIMARY KEY (postID, catID),
    FOREIGN KEY (postID) REFERENCES Post(postID) ON DELETE CASCADE,
    FOREIGN KEY (catID) REFERENCES Categories(catID) ON DELETE CASCADE
);

-- =========================
-- Likes Table
-- =========================
CREATE TABLE IF NOT EXISTS Likes (
    likeID      INTEGER PRIMARY KEY AUTOINCREMENT,
    postID      INTEGER,
    commentID   INTEGER,
    userID      INTEGER NOT NULL,
    value       INTEGER NOT NULL CHECK (value IN (1, -1)), -- 1 = like, -1 = dislike
    createdAt   DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (postID) REFERENCES Post(postID) ON DELETE CASCADE,
    FOREIGN KEY (commentID) REFERENCES Comments(commentID) ON DELETE CASCADE,
    FOREIGN KEY (userID) REFERENCES User(userID) ON DELETE CASCADE,
    CONSTRAINT unique_post_like UNIQUE (userID, postID),
    CONSTRAINT unique_comment_like UNIQUE (userID, commentID),
    CHECK (
        (postID IS NOT NULL AND commentID IS NULL) OR
        (postID IS NULL AND commentID IS NOT NULL)
    )
);

-- =========================
-- Optional: Sessions Table (for UUID tokens)
-- =========================
CREATE TABLE IF NOT EXISTS Sessions (
    sessionID   TEXT PRIMARY KEY NOT NULL, -- UUID string
    userID      INTEGER NOT NULL,
    valid       BOOLEAN,
    expiresAt   DATETIME NOT NULL,
    FOREIGN KEY (userID) REFERENCES User(userID) ON DELETE CASCADE
);

-- =========================
-- Indexes for performance
-- =========================
CREATE INDEX IF NOT EXISTS idx_post_user ON Post(userID);
CREATE INDEX IF NOT EXISTS idx_comment_post ON Comments(postID);
CREATE INDEX IF NOT EXISTS idx_likes_post ON Likes(postID);
CREATE INDEX IF NOT EXISTS idx_likes_comment ON Likes(commentID);
CREATE INDEX IF NOT EXISTS idx_postcat_cat ON Post_Cat(catID);
