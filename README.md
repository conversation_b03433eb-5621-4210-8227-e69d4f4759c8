# Forum Project

A full-featured web forum built with Go, SQLite, and modern web technologies.

## 👥 Team Members
- <PERSON><PERSON><PERSON>
- <PERSON>

## 🎯 Project Objectives
Create a web forum with the following features:
- User authentication (register/login)
- Post creation and management
- Commenting system
- Like/Dislike functionality
- Category-based organization
- Advanced filtering options

## 🛠️ Tech Stack
- **Backend**: Go (Golang)
- **Database**: SQLite3
- **Frontend**: HTML5, CSS3 (with CSS Variables)
- **Authentication**: Session-based with cookies
- **Containerization**: Docker

## 🌟 Features

### 🔐 Authentication
- User registration with email, username, and password
- Secure login with session management
- Password encryption (bcrypt)
- Session expiration

### 💬 Communication
- Create and view posts
- Comment on posts
- Associate posts with multiple categories
- Publicly visible content (no login required to view)

### 👍 Likes & Dislikes
- Like/dislike posts and comments
- View like/dislike counts
- Registered users only

### 🔍 Filtering
- Filter posts by categories
- View user's created posts (registered users)
- View user's liked posts (registered users)

## 🗄️ Database Schema
```
users
- id (PK)
- username (UNIQUE)
- email (UNIQUE)
- password_hash
- created_at

categories
- id (PK)
- name (UNIQUE)

topics
- id (PK)
- title
- content
- user_id (FK to users)
- created_at

posts
- id (PK)
- content
- user_id (FK to users)
- topic_id (FK to topics)
- created_at

comments
- id (PK)
- content
- user_id (FK to users)
- post_id (FK to posts)
- created_at

likes
- id (PK)
- user_id (FK to users)
- post_id (FK to posts, NULL for comments)
- comment_id (FK to comments, NULL for posts)
- is_like (BOOLEAN)

topic_categories
- topic_id (FK to topics)
- category_id (FK to categories)
- PRIMARY KEY (topic_id, category_id)
```

## 🚀 Getting Started

### Prerequisites
- Go 1.20+
- SQLite3
- Docker (optional)

### Local Development
1. Clone the repository
   ```bash
   git clone <repository-url>
   cd forum
   ```

2. Install dependencies
   ```bash
   go mod download
   ```

3. Set up environment variables
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Run migrations
   ```bash
   go run cmd/migrate/main.go
   ```

5. Start the server
   ```bash
   go run cmd/server/main.go
   ```

### Docker
building:  
docker build -t forum-app .

running container: 
docker run -p 8080:8080 forum-app

```bash
docker-compose up --build
```

## 🧪 Testing
Run tests with:
```bash
go test ./...
```

## 📝 API Endpoints

### Authentication
- `POST /register` - Register a new user
- `POST /login` - User login
- `POST /logout` - User logout

### Posts
- `GET /posts` - Get all posts
- `POST /posts` - Create a new post
- `GET /posts/:id` - Get a specific post
- `GET /posts/user/:user_id` - Get posts by user

### Comments
- `POST /posts/:id/comments` - Add comment to post
- `GET /posts/:id/comments` - Get comments for post

### Categories
- `GET /categories` - Get all categories
- `GET /categories/:id/posts` - Get posts by category

## 🎨 UI/UX
- Responsive design
- Clean, modern interface
- Intuitive navigation
- Accessible components

## 📚 Dependencies
- [Gorilla Mux](https://github.com/gorilla/mux) - HTTP router
- [sqlite3](https://github.com/mattn/go-sqlite3) - SQLite3 driver for Go
- [bcrypt](https://pkg.go.dev/golang.org/x/crypto/bcrypt) - Password hashing
- [uuid](https://github.com/gofrs/uuid) - UUID generation

## 📄 License
This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments
- 42 Network for the project guidelines
- Go community for amazing libraries and tools
