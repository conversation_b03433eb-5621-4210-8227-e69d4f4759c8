{{ define "title" }}My Posts{{ end }}
{{ define "myposts-content" }}
<div class="posts-container">
    {{ range .Posts }}
    <div class="post-wrapper">
        <article class="post">
            <div class="post-header">
                <div class="post-header-content">
                    <h2 class="post-title">{{ .Title }}</h2>
                    {{ if .Categories }}
                    <div class="category-bubble-container">
                        {{ range .Categories }}
                        <span class="category-bubble">
                            {{ . }}
                        </span>
                        {{ end }}
                    </div>
                    {{ end }}
                </div>
                <div class="post-meta">
                    <span class="post-author">Posted by {{ .Username }}</span>
                    <span class="post-date">{{ .Date.Format "Jan 02, 2006" }}</span>
                </div>
            </div>

            <div class="post-content">
                {{ .Content }}
            </div>

            <div class="post-actions">
                <!-- Comment GET form -->
                <form action="/comments" method="GET" style="display:inline;">
                    <input type="hidden" name="postid" value="{{.PostID}}">
                    <button type="submit" class="action-btn">
                        <i class="fas fa-comment"></i>
                        <span>Comment</span>
                    </button>
                </form>
                <form action="/like" method="POST">
                    <input type="hidden" name="postID" value="{{.PostID}}">
                    <input type="hidden" name="redirect" value="/myposts">
                    <button class="action-btn" name="value" value="1" type="submit">
                        <i class="fas fa-thumbs-up"></i>
                        <span>Like {{.Likes}}</span>
                    </button>
                    <button class="action-btn" name="value" value="-1" type="submit">
                        <i class="fas fa-thumbs-down"></i>
                        <span>Dislike {{.Dislikes}}</span>
                    </button>
                </form>
            </div>


        </article>
    </div>
    {{ else }}
    <div class="no-posts">
        <p>No posts available. Be the first to post something!</p>
    </div>
    {{ end }}
</div>
{{ end }}