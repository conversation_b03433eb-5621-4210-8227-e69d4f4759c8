<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Forum</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="/static/styles/colors.css">
    <link rel="stylesheet" href="/static/styles/style.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            min-height: 100vh;
            background: var(--gradient-primary);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            margin: 0;
            position: relative;
            color: var(--color-text);
        }

        /* .container {
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
            background-color: var(--color-lightest);
            padding: 2.5rem;
            border-radius: 16px;
            box-shadow: 0 8px 24px var(--color-shadow);
            position: relative;
            z-index: 1;
        } */

        h1 {
            font-size: 1.8rem;
            margin-bottom: 2rem;
            font-weight: 700;
            color: var(--color-darker);
            text-align: center;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-control {
            width: 100%;
            padding: 1rem;
            font-size: 1rem;
            border: 2px solid var(--color-light);
            border-radius: 8px;
            transition: all 0.2s ease;
            background-color: var(--color-lightest);
            color: var(--color-darkest);
            margin-bottom: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--color-mid);
            box-shadow: 0 0 0 2px var(--color-light);
        }

        .form-control::placeholder {
            color: var(--color-mid);
            opacity: 0.7;
        }

        .btn {
            display: block;
            width: 100%;
            padding: 0.8rem;
            font-size: 1rem;
            font-weight: 600;
            text-align: center;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 1rem;
        }

        .btn-primary {
            background-color: var(--color-darker);
            color: var(--color-lightest);
            padding: 1rem;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .btn-outline {
            background-color: transparent;
            color: var(--color-darker);
            border: 2px solid var(--color-darker);
        }

        .btn-google {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            background-color: var(--color-lightest);
            color: var(--color-darker);
            border: 2px solid var(--color-light);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--color-shadow);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-primary:hover {
            background-color: var(--color-dark);
        }

        .btn-outline:hover {
            background-color: var(--color-hover);
        }

        .divider {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 1.5rem 0;
            color: var(--color-mid);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid var(--color-light);
        }

        .divider:not(:empty)::before {
            margin-right: 0.5em;
        }

        .divider:not(:empty)::after {
            margin-left: 0.5em;
        }

        .signup-link {
            text-align: center;
            margin-top: 2rem;
            color: var(--color-mid);
            font-size: 0.9rem;
        }

        .signup-link a {
            color: var(--color-darker);
            text-decoration: none;
            font-weight: 600;
            margin-left: 4px;
            transition: color 0.2s ease;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        @media (max-width: 500px) {
            body {
                padding: 16px;
                background: var(--color-darker);
            }

            .container {
                width: 100%;
                padding: 1.5rem;
            }

            h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Create your account</h1>
        <!--         
        <button class="btn btn-google">
            <img src="https://www.google.com/favicon.ico" alt="Google" width="20" height="20">
            Sign up with Google
        </button>
        
        <button class="btn btn-outline">
            <i class="fab fa-github"></i> Sign up with GitHub
        </button>
        
        <div class="divider">or</div> -->

        <form method="POST" action="/register">
            <div class="form-group">
                <input name="username" type="text" id="username" class="form-control" placeholder="username" required>
            </div>
            <div class="form-group">
                <input name="email" type="email" id="email" class="form-control" placeholder="Email" required>
            </div>
            <div class="form-group">
                <input name="password" type="password" id="password" class="form-control" placeholder="Password"
                    required>
            </div>
            <button type="submit" class="btn btn-primary">Sign up</button>
        </form>

        <div class="signup-link">
            Already have an account? <a href="/login">Log in</a>
        </div>

        <div class="guest-link" style="margin-top: 1rem; text-align: center;">
            <a href="/home" style="color: var(--color-darker); font-weight: 500; text-decoration: none;">Continue as
                Guest</a>
        </div>
    </div>
</body>

</html>