{{ define "title" }}comments{{ end }}

{{ define "comments-content"}}

<div class="posts-container">
    {{ range .Posts }}
    <div class="post-wrapper">
        <article class="post">

            <!-- Back button (absolute in header) -->
            <a href="/home" class="back-button">← Back</a>

            <div class="post-header">
                <div class="post-header-content">
                    <h2 class="post-title">{{ .Title }}</h2>
                    {{ if .Categories }}
                    <div class="category-bubble-container">
                        {{ range .Categories }}
                        <span class="category-bubble">
                            {{ . }}
                        </span>
                        {{ end }}
                    </div>
                    {{ end }}
                </div>
                <div class="post-meta">
                    <span class="post-author">Posted by {{ .Username }}</span>
                    <span class="post-date">{{ .Date.Format "Jan 02, 2006" }}</span>
                </div>
            </div>

            <div class="post-content">
                {{ .Content }}
            </div>

            <div class="post-actions">
                <form action="/like" method="POST">
                    <input type="hidden" name="postID" value="{{.PostID}}">
                    <input type="hidden" name="redirect" value="/comments?postid={{.PostID}}">
                    <button class="action-btn" name="value" value="1" type="submit">
                        <i class="fas fa-thumbs-up"></i>
                        <span>Like {{.Likes}}</span>
                    </button>
                    <button class="action-btn" name="value" value="-1" type="submit">
                        <i class="fas fa-thumbs-down"></i>
                        <span>Dislike {{.Dislikes}}</span>
                    </button>
                </form>
            </div>
        </article>



        <div class="comments-section">
            <h3>Comments</h3>
            {{ if $.Comments }}
            <ul class="comment-list">
                {{ range $.Comments }}
                <li class="comment">
                    <strong>{{ .Username }}</strong>
                    <span class="comment-date">{{ .CreatedAt.Format "Jan 02, 2006 15:04" }}</span>
                    <p>{{ .Content }}</p>
                    <form action="/commentlike" method="POST">
                        <input type="hidden" name="postid" value="{{.PostID}}">
                        <input type="hidden" name="commentID" value="{{.CommentID}}">
                        <button class="action-btn" name="value" value="1" type="submit">
                            <i class="fas fa-thumbs-up"></i>
                            <span>Like {{.Likes}}</span>
                        </button>
                        <button class="action-btn" name="value" value="-1" type="submit">
                            <i class="fas fa-thumbs-down"></i>
                            <span>Dislike {{.Dislikes}}</span>
                        </button>
                    </form>
                </li>
                {{ end }}
            </ul>
            {{ else }}
            <p>No comments yet. Be the first to comment!</p>
            {{ end }}

            {{ if $.IsAuthenticated }}
            <!-- Comment Form -->
            <form method="POST" action="/comments">
                <input type="hidden" name="postid" value="{{ .PostID }}">
                <div>
                    <textarea name="content" placeholder="Write your comment..." class="post-textarea" required
                        style="height: 100px;" maxlength="300"></textarea>
                </div>
                <div>
                    <button type="submit" class="comment-one">Add Comment</button>
                </div>
            </form>
        </div>

        {{ else }}
        <p> Wanna comment? <a href="/login" class="comment-login-bubble">Login</a></p>
        {{ end }}

    </div>
    {{ else }}
    <div class="no-posts">
        <p>No posts available. Be the first to post something!</p>
    </div>
    {{ end }}
</div>

{{ end }}