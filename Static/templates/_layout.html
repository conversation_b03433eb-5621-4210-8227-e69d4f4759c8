{{ define "layout" }}
<html>

<head>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="/static/styles/colors.css">
    <link rel="stylesheet" href="/static/styles/style2.css">
    <link rel="stylesheet" href="/static/styles/posts.css">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta charset="UTF-8" name="description" content="Twitter Clone">
    <meta name="keywords" content="social media, LinkedIn, tweets, news">
    <meta name="author" content="Your Name">
    <meta name="robots" content="index, follow">
    <!-- ...existing code... -->
    {{ if eq .Template "index" }}
    <title>Home</title>
    {{ else if eq .Template "myposts" }}
    <title>My Posts</title>
    {{ else if eq .Template "myLikes" }}
    <title>My Likes</title>
    {{ else if eq .Template "comments" }}
    <title>Comments</title>
    {{ end }}
</head>

<body>
    <div class="header-space">
        <nav>
            <div class="nav-left">
                <h1>Forum</h1>
                <!-- <i class="fa-brands fa-twitter"></i> -->
                <!-- class="input-box" -->
                <div>
                    <!-- <i class="fa-solid fa-magnifying-glass"></i> -->
                    <!-- <input placeholder="Explore"/> -->
                </div>
            </div>

            {{if .IsAuthenticated}}

            <div class="nav-right">
                <a href="/home" title="Home">
                    <i class="fa-solid fa-house"></i>
                </a>

                <a href="/myposts" title="My Posts">
                    <i class="fa-solid fa-file-lines"></i>
                </a>

                <a href="/mylikes" title="Likes">
                    <i class="fa-solid fa-heart"></i>
                </a>
                <!--navigation categories-->
                <div class="categories-wrapper">
                    <a href="#categories-dropdown" class="categories-trigger" title="Categories">
                        <i class="fa-solid fa-tags"></i>
                    </a>
                    <div id="categories-dropdown" class="categories-dropdown">
                        <a href="#" class="category-close">
                            <i class="fas fa-times"></i>
                        </a>
                        <h3>Filter by Category</h3>
                        <form method="GET" action="/filter">
                            <div class="category-options">
                                {{ range .Categories }}
                                <label class="category-item">
                                    <input type="checkbox" name="categories" value="{{ .CatName }}" class="category-checkbox">
                                    <i class="{{ .Icon }}"></i>
                                    <span>{{ .CatName }}</span>
                                </label>
                                {{ end }}
                            </div>
                            <div class="category-actions">
                                <a href="/home" class="category-cancel">Clear All</a>
                                <button type="submit" class="category-apply">Apply Filters</button>
                            </div>
                        </form>
                    </div>
                </div>

                <a href="/logout" class="logout-btn" title="Logout">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>

            <!-- Floating post button and form -->
            <input type="checkbox" id="post-toggle" class="post-toggle">
            <div class="floating-post-button">
                <label for="post-toggle" class="create-post-btn">
                    <i class="fa-solid fa-plus"></i> Create Post
                </label>
            </div>

            <!-- Post creation form -->
            <div class="post-form-container">
                <!-- <form id="postForm" class="post-form" method="POST" action="/post" onsubmit="return handleFormSubmit(event)"> -->
                <form id="postForm" class="post-form" method="POST" action="/post">

                    <div class="post-form-header">
                        <h3>Create Post</h3>
                        <label for="post-toggle" class="close-form">
                            <i class="fas fa-times"></i>

                        </label>
                    </div>
                    <div class="form-group">
                        <input type="text" placeholder="Title" class="post-textarea" name="title" required>
                        <textarea placeholder="What's happening?" class="post-textarea" name="content" required
                            style="height: 150px;" maxlength="300"></textarea>
                        <div id="selected-categories" class="selected-categories">
                            <!-- Selected categories will appear here -->
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-post">Post</button>
                        <div class="category-dropdown">
                            <!-- <button type="button" class="btn-category" onclick="this.nextElementSibling.classList.toggle('show')"> -->
                            <button type="button" class="btn-category">

                                <span>Category</span>
                                <span class="dropdown-arrow">▼</span>
                            </button>
                            <div class="category-dropdown-content">
                                {{ range .Categories }}
                                <label class="category-item">
                                    <input type="checkbox" name="categories" value="{{ .CatName }}"
                                        class="category-checkbox">
                                    <i class="{{ .Icon }}"></i>
                                    <span>{{ .CatName }}</span>
                                </label>
                                {{ end }}
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            {{ else }}

            <div class="nav-right">
                <a href="/home" title="Home">
                    <i class="fa-solid fa-house"></i>
                </a>

                <div class="categories-wrapper">
                    <a href="#categories-dropdown" class="categories-trigger" title="Categories">
                        <i class="fa-solid fa-tags"></i>
                    </a>
                    <div id="categories-dropdown" class="categories-dropdown">
                        <a href="#" class="category-close">
                            <i class="fas fa-times"></i>
                        </a>
                        <h3>Filter by Category</h3>
                        <div class="category-options">
                            <label class="category-item">
                                <input type="checkbox" name="categories" value="General" class="category-checkbox">
                                <i class="fas fa-hashtag"></i>
                                <span>General</span>
                            </label>
                            <label class="category-item">
                                <input type="checkbox" name="categories" value="Technology" class="category-checkbox">
                                <i class="fas fa-laptop-code"></i>
                                <span>Technology</span>
                            </label>
                            <label class="category-item">
                                <input type="checkbox" name="categories" value="Gaming" class="category-checkbox">
                                <i class="fas fa-gamepad"></i>
                                <span>Gaming</span>
                            </label>
                            <label class="category-item">
                                <input type="checkbox" name="categories" value="Movies" class="category-checkbox">
                                <i class="fas fa-film"></i>
                                <span>Movies</span>
                            </label>
                            <label class="category-item">
                                <input type="checkbox" name="categories" value="Music" class="category-checkbox">
                                <i class="fas fa-music"></i>
                                <span>Music</span>
                            </label>
                            <label class="category-item">
                                <input type="checkbox" name="categories" value="Sports" class="category-checkbox">
                                <i class="fas fa-futbol"></i>
                                <span>Sports</span>
                            </label>
                            <label class="category-item">
                                <input type="checkbox" name="categories" value="Food" class="category-checkbox">
                                <i class="fas fa-utensils"></i>
                                <span>Food</span>
                            </label>
                            <label class="category-item">
                                <input type="checkbox" name="categories" value="Travel" class="category-checkbox">
                                <i class="fas fa-plane"></i>
                                <span>Travel</span>
                            </label>
                            <label class="category-item">
                                <input type="checkbox" name="categories" value="Fashion" class="category-checkbox">
                                <i class="fas fa-tshirt"></i>
                                <span>Fashion</span>
                            </label>
                            <label class="category-item">
                                <input type="checkbox" name="categories" value="Science" class="category-checkbox">
                                <i class="fas fa-atom"></i>
                                <span>Science</span>
                            </label>
                        </div>
                        <div class="category-actions">
                            <a href="#" class="category-cancel">Clear All</a>
                            <button type="button" class="category-apply">Apply Filters</button>
                        </div>
                    </div>
                </div>

                <div class="auth-buttons" style="display: flex; gap: 0.625rem;">
                    <a href="/login" class="btn btn-outline"
                        style="text-decoration: none; padding: 0.5rem 1rem; border-radius: 1.25rem; font-weight: 600; color: #350835; border: 0.0625rem solid #350835;">
                        Log in
                    </a>
                    <a href="/register" class="btn btn-primary"
                        style="text-decoration: none; padding: 0.5rem 1rem; border-radius: 1.25rem; font-weight: 600; background-color: #350835; color: white; border: none;">
                        Sign up
                    </a>
                </div>

            </div>

            {{ end }}


        </nav>

    </div>
    <main>
        {{ if eq .Template "index" }}
        {{ template "index-content" . }}

        {{ else if eq .Template "myLikes" }}
        {{ template "myLikes-content" . }}

        {{ else if eq .Template "myposts" }}
        {{ template "myposts-content" . }}

        {{ else if eq .Template "comments" }}
        {{ template "comments-content" . }}

        {{ end }}
    </main>
</body>

</html>

{{ end }}

