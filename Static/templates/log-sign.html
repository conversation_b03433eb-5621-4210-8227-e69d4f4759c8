<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign in </title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="/static/styles/colors.css">
    <link rel="stylesheet" href="/static/styles/style.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: TwitterChirp, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }

        body {
            min-height: 100vh;
            background: var(--gradient-primary);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            margin: 0;
            position: relative;
            color: var(--color-text);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .logo {
            margin: 20px 0 40px;
            color: #1d9bf0;
            font-size: 2.5rem;
        }

        .container {
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
        }

        h1 {
            font-size: 1.8rem;
            margin-bottom: 2rem;
            font-weight: 700;
            color: var(--color-darker);
            text-align: center;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-control {
            width: 100%;
            padding: 1rem;
            font-size: 1rem;
            border: 2px solid var(--color-light);
            border-radius: 8px;
            transition: all 0.2s ease;
            background-color: var(--color-lightest);
            color: var(--color-darkest);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--color-mid);
            box-shadow: 0 0 0 2px var(--color-light);
        }

        .form-control::placeholder {
            color: var(--color-mid);
            opacity: 0.7;
        }

        .btn {
            display: block;
            width: 100%;
            padding: 0.8rem;
            font-size: 1rem;
            font-weight: 600;
            text-align: center;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 1rem;
        }

        .btn-primary {
            background-color: var(--color-darker);
            color: var(--color-lightest);
            padding: 1rem;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .btn-outline {
            background-color: transparent;
            color: var(--color-darker);
            border: 2px solid var(--color-darker);
        }

        .btn-google {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            background-color: var(--color-lightest);
            color: var(--color-darker);
            border: 2px solid var(--color-light);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--color-shadow);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-primary:hover {
            background-color: var(--color-dark);
        }

        .btn-outline:hover {
            background-color: var(--color-hover);
        }

        .divider {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 1.5rem 0;
            color: var(--color-mid);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid var(--color-light);
        }

        .divider:not(:empty)::before {
            margin-right: 1rem;
        }

        .divider:not(:empty)::after {
            margin-left: 1rem;
        }

        .forgot-password {
            display: block;
            text-align: center;
            color: var(--color-dark);
            text-decoration: none;
            margin: 1.5rem 0 2rem;
            font-size: 0.9rem;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .forgot-password:hover {
            color: var(--color-darker);
            text-decoration: underline;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        .signup-link {
            text-align: center;
            margin-top: 2rem;
            color: var(--color-mid);
            font-size: 0.9rem;
        }

        .signup-link a {
            color: var(--color-darker);
            text-decoration: none;
            font-weight: 600;
            margin-left: 4px;
            transition: color 0.2s ease;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        @media (max-width: 500px) {
            body {
                padding: 16px;
                background: var(--color-darker);
            }

            .container {
                width: 100%;
                padding: 1.5rem;
            }

            h1 {
                font-size: 1.5rem;
            }

            .logo {
                font-size: 2.5rem;
                margin-bottom: 2rem;
            }
        }
    </style>
</head>

<body>

    <div class="container">
        <h1>Sign in to Forum</h1>

        <form method="POST" action="/login">
            <div class="form-group">
                <input type="text" id="email" name="email" placeholder="<EMAIL>" class="form-control"
                    required />
            </div>

            <div class="form-group">
                <input type="password" id="password" name="password" placeholder="Password" class="form-control"
                    required />
            </div>

            <button type="submit" class="btn btn-primary">Login</button>
        </form>

        <!-- <button class="btn btn-outline">
            Forgot password?
        </button> -->

        <div class="signup-link">
            Don't have an account? <a href="/register">Sign up</a>
        </div>

        <div class="guest-link" style="margin-top: 1rem; text-align: center;">
            <a href="/home" style="color: var(--color-darker); font-weight: 500; text-decoration: none;">Continue as
                Guest</a>
        </div>
    </div>
</body>

</html>

</div>
</body>

</html>