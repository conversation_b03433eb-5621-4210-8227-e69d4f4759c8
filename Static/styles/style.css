/* log-sign.html */

/* From Uiverse.io by SelfMadeSystem */ 
.form-container {
  position: relative;
  perspective: 50rem;
  width: 50rem;
  height: 50rem;
}

#switch {
  display: none;
}

.sign-up-form,
.log-in-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sign-up-container,
.log-in-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  inset: 0;
  margin: auto;
  width: 20rem;
  height: 20rem;
  background: #2c164f;
  color: #fff;
  padding: 1rem;
  border-radius: 0.5rem;
}

.switch-label {
  font-size: x-small;
  text-decoration: underline;
  cursor: pointer;
}

.form-title {
  font-size: x-large;
  font-weight: bold;
}

:where(.sign-up-form, .log-in-form) label {
  font-size: x-small;
  padding-top: 0.5rem;
  padding-left: 0.25rem;
}

:where(.sign-up-form, .log-in-form) input {
  background-color: #fff2;
  box-sizing: border-box;
  color: white;
  border: 0;
  border-bottom: 2px solid #cfb2ff;
  padding: 0.25rem;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

:where(.sign-up-form, .log-in-form) input:focus-visible {
  outline: none;
  border: 1px solid white;
  border-bottom: 2px solid #cfb2ff;
}

:where(.sign-up-form, .log-in-form) button {
  margin-top: auto;
  background-color: #ffffff4b;
  color: white;
  border: 0;
  padding: 0.25rem;
  border-radius: 0.25rem;
  cursor: pointer;
}

.forgot-pwd {
  font-size: x-small;
  text-decoration: underline;
  margin-left: auto;
  cursor: pointer;
}

/* Social yoinked from uiverse.io/Yaya12085/short-panda-24 */

.social-message {
  display: flex;
  align-items: center;
  padding-top: 1rem;
}

.line {
  height: 1px;
  flex: 1 1 0%;
  background-color: #cfb2ff;
}

.social-message .message {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #fff;
}

.social-icons {
  display: flex;
  justify-content: center;
}

.social-icons .icon {
  border-radius: 0.125rem;
  padding: 0.75rem;
  padding-bottom: 0.25rem;
  border: none;
  background-color: transparent;
  margin-left: 8px;
  cursor: pointer;
}

.social-icons .icon svg {
  height: 1.25rem;
  width: 1.25rem;
  fill: #fff;
}

.sign-up-container {
  animation: hello 0.5s linear forwards;
}

.log-in-container {
  animation: unhello 0.5s linear forwards;
}

#switch:checked ~ .sign-up-container {
  animation: unhello 0.5s linear forwards;
}

#switch:checked ~ .log-in-container {
  animation: hello 0.5s linear forwards;
}

@keyframes hello {
  0% {
    transform: rotateY(-90deg);
    display: none;
  }
  50% {
    transform: rotateY(-90deg);
    display: flex;
  }
  100% {
    transform: rotateY(0deg);
    display: flex;
  }
}

@keyframes unhello {
  0% {
    transform: rotateY(0deg);
    display: flex;
  }
  50% {
    transform: rotateY(90deg);
    display: flex;
  }
  100% {
    transform: rotateY(90deg);
    display: none;
  }
}

/* From Uiverse.io by javierBarroso */ 
.wrapper {
  --background: #62abff;
  --icon-color: #414856;
  --shape-color-01: #b8cbee;
  --shape-color-02: #7691e8;
  --shape-color-03: #fdd053;
  --width: 90px;
  --height: 90px;
  --border-radius: var(--height);
  width: var(--width);
  height: var(--height);
  position: relative;
  border-radius: var(--border-radius);
  display: flex;
  justify-content: center;
  align-items: center;
}
.wrapper .btn {
  background: var(--background);
  width: var(--width);
  height: var(--height);
  position: relative;
  z-index: 3;
  border-radius: var(--border-radius);
  box-shadow: 0 10px 30px rgba(65, 72, 86, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-animation: plus-animation-reverse 0.5s ease-out forwards;
  animation: plus-animation-reverse 0.5s ease-out forwards;
}
.wrapper .btn::before,
.wrapper .btn::after {
  content: "";
  display: block;
  position: absolute;
  border-radius: 4px;
  background: #fff;
}
.wrapper .btn::before {
  width: 4px;
  height: 28px;
}
.wrapper .btn::after {
  width: 28px;
  height: 4px;
}
.wrapper .tooltip {
  width: fit-content;
  height: 75px;
  border-radius: 70px;
  position: absolute;
  background: #fff;
  z-index: 2;
  padding: 20px 35px;
  box-shadow: 0 10px 30px rgba(65, 72, 86, 0.05);
  opacity: 0;
  top: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  transition: opacity 0.15s ease-in, top 0.15s ease-in, width 0.15s ease-in;
}
.wrapper .tooltip > span {
  position: relative;
  width: 100%;
  white-space: nowrap;
  opacity: 0;
}
.wrapper .tooltip > svg {
  width: 100%;
  height: 26px;
  display: block;
  justify-content: space-around;
  align-items: center;
  cursor: pointer;
}
.wrapper .tooltip > svg .icon {
  fill: none;
  stroke: var(--icon-color);
  stroke-width: 2px;
  stroke-linecap: round;
  stroke-linejoin: round;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}
.wrapper .tooltip > svg:hover .icon {
  opacity: 1;
}
.wrapper .tooltip::after {
  content: "";
  width: 20px;
  height: 20px;
  background: #fff;
  border-radius: 3px;
  position: absolute;
  left: 50%;
  margin-left: -10px;
  bottom: -8px;
  transform: rotate(45deg);
  z-index: 0;
}
.wrapper > svg {
  width: 300px;
  height: 300px;
  position: absolute;
  z-index: 1;
  transform: scale(0);
}
.wrapper > svg .shape {
  fill: none;
  stroke: none;
  stroke-width: 3px;
  stroke-linecap: round;
  stroke-linejoin: round;
  transform-origin: 50% 20%;
}
.wrapper input {
  height: 100%;
  width: 100%;
  border-radius: var(--border-radius);
  cursor: pointer;
  position: absolute;
  z-index: 5;
  opacity: 0;
}
.wrapper input:checked ~ svg {
  -webkit-animation: pang-animation 1.2s ease-out forwards;
  animation: pang-animation 1.2s ease-out forwards;
}
.wrapper input:checked ~ svg .shape:nth-of-type(1) {
  transform: translate(25px, 30%) rotate(40deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(2) {
  transform: translate(-4px, 30%) rotate(80deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(3) {
  transform: translate(12px, 30%) rotate(120deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(4) {
  transform: translate(8px, 30%) rotate(160deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(5) {
  transform: translate(21px, 30%) rotate(200deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(6) {
  transform: translate(0px, 30%) rotate(240deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(7) {
  transform: translate(17px, 30%) rotate(280deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(8) {
  transform: translate(-3px, 30%) rotate(320deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(9) {
  transform: translate(25px, 30%) rotate(360deg);
}
.wrapper input:checked ~ .btn {
  -webkit-animation: plus-animation 0.5s ease-out forwards;
  animation: plus-animation 0.5s ease-out forwards;
}
.wrapper input:checked ~ .tooltip {
  width: fit-content;
  padding: 30px;
  -webkit-animation: stretch-animation 1s ease-out forwards 0.15s;
  animation: stretch-animation 1s ease-out forwards 0.15s;
  top: -90px;
  opacity: 1;
}

.wrapper input:checked ~ .tooltip > span {
  opacity: 1;
}

@-webkit-keyframes pang-animation {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}

@keyframes pang-animation {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}
@-webkit-keyframes plus-animation {
  0% {
    transform: rotate(0) scale(1);
  }
  20% {
    transform: rotate(60deg) scale(0.93);
  }
  55% {
    transform: rotate(35deg) scale(0.97);
  }
  80% {
    transform: rotate(48deg) scale(0.94);
  }
  100% {
    transform: rotate(45deg) scale(0.95);
  }
}
@keyframes plus-animation {
  0% {
    transform: rotate(0) scale(1);
  }
  20% {
    transform: rotate(60deg) scale(0.93);
  }
  55% {
    transform: rotate(35deg) scale(0.97);
  }
  80% {
    transform: rotate(48deg) scale(0.94);
  }
  100% {
    transform: rotate(45deg) scale(0.95);
  }
}
@-webkit-keyframes plus-animation-reverse {
  0% {
    transform: rotate(45deg) scale(0.95);
  }
  20% {
    transform: rotate(-15deg);
  }
  55% {
    transform: rotate(10deg);
  }
  80% {
    transform: rotate(-3deg);
  }
  100% {
    transform: rotate(0) scale(1);
  }
}
@keyframes plus-animation-reverse {
  0% {
    transform: rotate(45deg) scale(0.95);
  }
  20% {
    transform: rotate(-15deg);
  }
  55% {
    transform: rotate(10deg);
  }
  80% {
    transform: rotate(-3deg);
  }
  100% {
    transform: rotate(0) scale(1);
  }
}
@-webkit-keyframes stretch-animation {
  0% {
    transform: scale(1, 1);
  }
  10% {
    transform: scale(1.1, 0.9);
  }
  30% {
    transform: scale(0.9, 1.1);
  }
  50% {
    transform: scale(1.05, 0.95);
  }
  100% {
    transform: scale(1, 1);
  }
}
@keyframes stretch-animation {
  0% {
    transform: scale(1, 1);
  }
  10% {
    transform: scale(1.1, 0.9);
  }
  30% {
    transform: scale(0.9, 1.1);
  }
  50% {
    transform: scale(1.05, 0.95);
  }
  100% {
    transform: scale(1, 1);
  }
}

.socials {
  position: fixed;
  display: block;
  left: 20px;
  bottom: 20px;
}
.socials > a {
  display: block;
  width: 30px;
  opacity: 0.2;
  transform: scale(var(--scale, 0.8));
  transition: transform 0.3s cubic-bezier(0.38, -0.12, 0.24, 1.91);
}
.socials > a:hover {
  --scale: 1;
}
