@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500&display=swap');
    body{
        margin: 0;
        background-color: #522B5B;
        font-family: 'Roboto', sans-serif;
    }
    header{
        padding: 0 2.5rem;
        /* background: rgba(153, 178, 206, 0.97); */
    }
    header i{
        color: #350835;
        font-size: 2.5em;
    }
    nav{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
    }
    .nav-right {
        display: flex;
        gap: 2.5rem;
        align-items: center;
    }
     .nav-right a i,
    .logout-btn i {
        font-size: 1.2em;
        color: #DFB6B2;
        transition: color 0.2s ease;
    }
    .nav-right a:hover,
    .nav-right i:hover,
    .logout-btn:hover i {
        color: white;
    }
    .nav-left{
        color: #350835 ;
        display: flex;
        gap: 0.625rem;
        align-items: center;
    }
    
    .logout-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        transition: background-color 0.2s ease;
        text-decoration: none;
    }
    
    .logout-btn:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

   
    
    /* Categories Dropdown Styles */
    .categories-wrapper {
        position: relative;
        display: inline-block;
    }
    
    .categories-trigger {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: white;
        padding: 0.625rem;
        border-radius: 50%;
        transition: all 0.2s ease;
    }
    
    /* .categories-trigger:hover {
        background-color: rgba(29, 161, 242, 0.1);
        color: #350835;
    } */
    
    .categories-dropdown {
        display: none;
        position: absolute;
        top: 100%;
        right: 0;
        width: 300px;
        background: #fbe4d8;
        /* border: 1px solid #38444d; */
        border-radius: 16px;
        padding: 20px;
        margin-top: 10px;
        box-shadow: 0 0.25rem 1.25rem rgba(0, 0, 0, 0.5);
        z-index: 1000;
        opacity: 0;
        transform: translateY(10px);
        transition: opacity 0.2s ease, transform 0.2s ease;
    }
    
    .categories-dropdown:target {
        display: block;
        opacity: 1;
        transform: translateY(0);
    }
    
    .category-close {
        position: absolute;
        top: 15px;
        right: 15px;
        color: #350835;
        cursor: pointer;
        font-size: 1.1em;
        opacity: 0.7;
        transition: all 0.2s ease;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }
    
    .category-close:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.1);
    }
    
    .category-close:hover {
        color: #350835;
    }
    
    .categories-dropdown h3 {
        color: #350835;
        margin: 0 0 1.25rem 0;
        font-size: 1.2em;
        text-align: center;
    }
    
    .category-options {
        display: flex;
        flex-direction: column;
        gap: 0.625rem;
        margin-bottom: 1.25rem;
        max-height: 15.625rem;
        overflow-y: auto;
        padding-right: 0.3125rem;
    }
    
    .category-option {
        display: flex;
        align-items: center;
        padding: 0.625rem 0.9375rem;
        border-radius: 0.5rem;
        background-color: #350835;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .category-option:hover {
        background-color: #22303c;
    }
    
    .category-option input[type="checkbox"] {
        margin-right: 0.625rem;
        width: 1em;
        height: 1em;
        cursor: pointer;
    }
    
    .category-option span {
        color: #fff;
        font-size: 0.95em;
    }
    
    .category-actions {
        display: flex;
        justify-content: space-between;
        padding-top: 0.9375rem;
        /* border-top: 1px solid #38444d; */
    }
    
    .category-cancel {
        background: transparent;
        /* border: 1px solid #38444d; */
        color: #350835;
        padding: 0.5rem 1rem;
        border-radius: 1.25rem;
        text-decoration: none;
        font-size: 0.9em;
        transition: background-color 0.2s ease;
    }
    
    .category-cancel:hover {
        background-color: rgba(29, 161, 242, 0.1);
    }
    
    .category-apply {
        background: #350835;
        border: none;
        color: white;
        padding: 0.5rem 1.25rem;
        border-radius: 1.25rem;
        font-weight: bold;
        cursor: pointer;
        font-size: 0.9em;
        transition: background-color 0.2s ease;
    }
    
    .category-apply:hover {
        background: #4e0d4e;
    }
    
    /* Post Creation Categories Dropdown Specific Styles */
    .post-types .categories-wrapper {
        position: relative;
    }
    
    .post-types .categories-trigger {
        display: flex;
        align-items: center;
        gap: 0.3125rem;
        text-decoration: none;
        /* color: #350835; */
        padding: 0.3125rem 0.625rem;
        border-radius: 0.9375rem;
        transition: all 0.2s ease;
        cursor: pointer;
    }
    
    /* .post-types .categories-trigger:hover {
        background-color: rgba(29, 161, 242, 0.1);
    }
     */
    .post-types .categories-dropdown {
        position: fixed;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) scale(0.9) !important;
        width: 90%;
        max-width: 400px;
        max-height: 80vh;
        background: #2d0a2d;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
        z-index: 9999 !important;
        opacity: 0;
        pointer-events: none;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        margin: 0 !important;
        display: flex;
        flex-direction: column;
    }
    
    .category-options {
        max-height: 50vh;
        overflow-y: auto;
        padding-right: 10px;
        margin-bottom: 15px;
    }
    
    .category-options::-webkit-scrollbar {
        width: 6px;
    }
    
    .category-options::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 3px;
    }
    
    .category-options::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
    }
    
    .category-options::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.3);
    }
    
    .category-toggle:checked ~ .categories-dropdown {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1) !important;
        pointer-events: auto;
    }
    
    .post-types .categories-dropdown h3 {
        font-size: 1.25em;
        margin: 0 0 20px 0;
        color: #e7e9ea;
        text-align: center;
        font-weight: 500;
    }
    
    .post-types .category-options {
        max-height: 17.5rem;
        padding: 0;
    }
    
    .category-toggle {
        display: none;
    }
    
    .category-radio {
        display: none;
    }
    
    .post-types .category-option {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        color: #e7e9ea;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        margin: 8px 0;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .post-types .category-option:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(5px);
    }
    
    .post-types .category-option:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
    
    /* Show dropdown when toggle is checked */
    .category-toggle:checked ~ .categories-dropdown {
        display: block;
        opacity: 1;
        transform: translateY(0);
    }
    
    /* Style for selected category */
    .category-radio:checked + .category-circle {
        transform: scale(1.2);
        /* box-shadow: 0 0 0 2px white, 0 0 0 4px rgba(255,255,255,0.2); */
    }
    
    .category-circle {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 12px;
        transition: all 0.2s ease;
    }
    
    .selected-category {
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.625rem;
        margin-right: 0.3125rem;
        transition: background-color 0.2s ease;
    }
    
    .selected-category i {
        color: white;
    }
    
    /* Update selected category color based on radio selection */
    /* #technology:checked ~ .categories-trigger .selected-category {
        background-color: #350835;
    } */
/*     
    #science:checked ~ .categories-trigger .selected-category {
        background-color: #17bf63;
    }
    
    #business:checked ~ .categories-trigger .selected-category {
        background-color: #794bc4;
    }
    
    #entertainment:checked ~ .categories-trigger .selected-category {
        background-color: #e0245e;
    }
    
    #sports:checked ~ .categories-trigger .selected-category {
        background-color: #ffad1f;
    }
     */
    /* Hide default radio button */
    .category-radio {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    /* Style the custom radio button */
    .category-option {
        position: relative;
        padding-left: 2.1875rem;
        cursor: pointer;
        user-select: none;
    }
    
    /* Style the indicator (dot/circle) */
    .category-option .category-circle {
        /* position: absolute;
        left: 0.625rem;
        top: 50%; */
        transform: translateY(-50%);
        transition: all 0.2s ease;
    }
    
    /* Show a checkmark when checked */
    .category-radio:checked ~ .category-circle::after {
        content: '';
        position: absolute;
        width: 0.5rem;
        height: 0.5rem;
        border-radius: 50%;
        background: white;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    .twitter-blue{
        display: flex;
        background-color: white;
        padding: 0.25rem 0.5rem;
        gap: 0.625rem;
        border-radius: 6.25rem;
        color: white;
        background: #350835;
        cursor: pointer;
        font-weight: 300;
    }
    .twitter-blue:hover{
        background: #4e0d4e;
    }
    .twitter-blue p{
        padding: 0.3125rem 0.125rem;
        margin: 0 auto;
        font-size: 0.9375rem;
    }
    

    .input-box input{
        font-size: 0.875rem;            
        border: none;
        background-color: #350835;
        width: 100%;
        color: white;
    }
    .input-box input:focus{
        outline: none;
    }
    .input-box i{
        font-size: 0.8rem;
        color: rgb(192, 192, 192);
    }
    main{
        font-family: 'Roboto', sans-serif;
        margin: 0 auto;
        width: fit-content;
        display: grid;
        grid-template-columns: 18.75rem 37.5rem 18.75rem;
        padding: 0 1.875rem;
        max-width: 100%;
        box-sizing: border-box;
    }
    .profile {
        width: 18.75rem;
        font-family: 'Roboto', sans-serif;
        max-width: 100%;
    }
    .profile-header{
        text-align: center;
        border-radius: 0.75rem 0.75rem 0 0;
        background: rgb(13, 104, 231);
        background: url("https://64.media.tumblr.com/ad7c23f923e144352007a7de049c98ac/92af8a19abae2b8-a4/s640x960/ddc63bf27f5d84ec18e39bfba7d5e2ae15b2ddfb.png");
        background-position: center;
        background-size: cover;
        padding-top: 56.25%; /* 16:9 Aspect Ratio */
        position: relative;
    }
    .profile img {
        width: 4.5em;
        height: 4.5em;
        border-radius: 50%;
        position: absolute;
        bottom: -2.25rem;
        left: 50%;
        transform: translateX(-50%);
        border: 0.1875rem solid #522B5B;
    }
    .profile-body {
        background-color: #522B5B;
        border-radius: 0 0 0.9375rem 0.9375rem;
        margin-top: 2.25rem;
        position: relative;
    }
    .profile-info {
        margin: 0 1.875rem;
        padding-top: 2.1875rem;
        padding-bottom: 0.625rem;
        text-align: center;
    }
    .profile-info h1 {
        font-size: 1.4375rem;
        margin-top: 0;
        color: #F5F8FA;
        font-weight: 300;
        margin-bottom: 0;
    }
    .profile-info h2 {
        font-size: 0.9375rem;
        color: #657786;
        margin: 0.3125rem 0;
    }
    .profile-info p{
        font-size: 0.8125rem;
        color: #AAB8C2;
        margin: 0.3125rem 0 0;
    }
    
    hr{
        border-color: #522B5B;
        border-width: 0.1;
        margin: 0;
    }
    
    .stat-box {
        padding: 0.3125rem 0;
        display: flex;
    }
    
    .stat-box div {
        width: 100%;
        text-align: center;
    }
    
    .stat-box h3 {
        color: #F5F8FA;
        margin-bottom: 0;
    }
    
    .stat-box p {
        color: #AAB8C2;
        margin: 0.5rem 0 0;
        font-size: 0.75rem;
    }
    
    .my-profile {
        padding: 0.625rem 0;
        text-align: center;
    }
    .my-profile p{
        color: #350835;
    }
    .follow-sugggestion{
        font-family: 'Roboto', sans-serif;
        background-color: #522B5B;
        width: 16.875rem;
        border-radius: 0.75rem;
        padding: 0.875rem;
        margin: 0.625rem 0;
        box-sizing: border-box;
    }
    .follow-sugggestion h4{
        color: #F5F8FA;
        margin: 0 0 1.25rem 0;
        font-size: 1.125rem;
        font-weight: 500;
    }
    .follow-profile{
        display: flex;
        gap: 0.3125rem;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.625rem;
    }

    .follow-profile img{
        border-radius: 50%;
        height: 3rem;
        width: 3rem;
        flex-shrink: 0;
    }

    .follow-info h5{
        margin: 0;
        font-size: 0.9375rem;
        color: #F5F8FA;
        font-weight: 400;
    }
    .follow-info p{
        margin: 0.1875rem;
        font-size: 0.75rem;
        color: #657786;
    }
    .follow-btn{
        background: white;
        padding: 0.4375rem 0.9375rem;
        text-align: center;
        height: fit-content;
        border-radius: 100px;
        font-weight: 500;
        font-size: 0.75rem;
        cursor: pointer;
    }
    .follow-btn:hover{
        background: rgb(195, 194, 194);
    }
    .show-more{
        color: #350835;
        font-size: 0.75rem;
        margin: 1.5625rem 0 0.3125rem 0;
        cursor: pointer;
    }

    .show-more:hover{
        color: #4c0e4c;
    }

    .posts-section{
        background: #190019;
    }

    .post-head{
        display: flex;
        justify-content: space-between;
    }
    .post-head img{
        height: 3rem;
        width: 3rem;
        border-radius: 50%;
    }
    .post-user{
        display: flex;
        gap: 0.625rem;
        padding: 5px;
    }
    .user{
        display: flex;
        gap: 0.3125rem;
    }
    .user p{
        margin: 0;
        font-size: 0.75rem;
        color: grey;
    }
    .user h6{
        margin: 0;
        font-size: 14px;
        color: #F5F8FA;
    }
    .post-content p{
        color: #F5F8FA;
        margin: 0.625rem 0;
        font-size: 14px;
    }
    .post img{
        width: 100%;
        margin-top: 10px;
    }
    .post p{
        font-size: 0.75rem;
        font-weight: 200;
        color: #F5F8FA;
        margin: 0px;
    }
    .react{
        display: flex;
        justify-content: space-around;
        margin-top: 5px;
        position: relative;
    }
    
    .comment-trigger {
        position: relative;
    }
    
    .comment-link {
        display: flex;
        align-items: center;
        gap: 0.3125rem;
        text-decoration: none;
        color: inherit;
    }
    
    /* Comment form overlay */
    .comment-form {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        /* background: rgba(0, 0, 0, 0.7); */
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s;
        backdrop-filter: blur(5px);
    }
    
    .comment-form:target {
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 1;
        visibility: visible;
    }
/*     
    .comment-form-inner {
        width: 90%;
        max-width: 500px;
        background: #15202b;
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.5);
        transform: translateY(20px);
        transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 1px solid #38444d;
        position: relative;
    } */
    
    .comment-form:target .comment-form-inner {
        transform: translateY(0);
    }
    
    /* Close button */
    .comment-close {
        position: absolute;
        top: 0.9375rem;
        right: 0.9375rem;
        color: #8899a6;
        font-size: 1.2rem;
        text-decoration: none;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s;
    }
    
    .comment-close:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
    }
    
    .comment-form-inner {
        display: flex;
        flex-direction: column;
        gap: 15px;
        padding-top: 40px;
    }
    
    .comment-input {
        display: flex;
        gap: 0.75rem;
        align-items: flex-start;
        padding: 0.5rem 0;
    }
    
    .comment-avatar {
        width: 2.25rem;
        height: 2.25rem;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .comment-text {
        flex: 1;
        background: transparent;
        border: 1px solid #38444d;
        border-radius: 1.25rem;
        padding: 12px 15px;
        color: white;
        font-size: 15px;
        outline: none;
        min-height: 100px;
        resize: none;
        font-family: inherit;
        line-height: 1.4;
        transition: all 0.2s ease;
    }
    
    .comment-text:focus {
        border-color: #350835;
        box-shadow: 0 0 0 2px rgba(29, 161, 242, 0.2);
    }
    
    .comment-actions {
        display: flex;
        justify-content: flex-end;
        gap: 0.75rem;
        padding-top: 10px;
        /* border-top: 1px solid #38444d; */
        margin-top: 5px;
    }
    
    .comment-submit {
        background: #350835;
        color: white;
        border: none;
        border-radius: 1.25rem;
        padding: 0.5rem 1.25rem;
        font-weight: bold;
        cursor: pointer;
        font-size: 15px;
        transition: all 0.2s ease;
    }
    
    .comment-submit:hover {
        background: #4e0d4e;
        transform: translateY(-1px);
    }
    
    .comment-submit:active {
        transform: translateY(0);
    }
    
    .comment-cancel {
        color: #8899a6;
        text-decoration: none;
        font-size: 15px;
        display: flex;
        align-items: center;
        padding: 8px 15px;
        border-radius: 1.25rem;
        transition: all 0.2s ease;
    }
    
    .comment-cancel:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #350835;
        text-decoration: none;
    }
    .react div{
        display: flex;
        gap: 0.3125rem;
        align-items: center;
        margin-top: 5px;
        cursor: pointer;
        border-radius: 5px;
    }
    .react div p{
        font-size: 0.75rem;
        color: grey;
        margin: 0.25rem;
    }
    .react div i{
        color: grey;
        font-size: 0.75rem;
    }
    .react div:nth-child(1){
        color: rgb(226, 226, 226);
    }

    .posts{
        background-color: #522B5B;
        margin: 0px 20px 10px 20px;
        border-radius: 1.25rem;
        padding: 0.625rem;
    }
    
    /* Comment form styles */
    .comment-form {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        /* background: #15202b;
        border: 1px solid #38444d; */
        border-radius: 16px;
        padding: 20px;
        width: 90%;
        max-width: 500px;
        z-index: 1000;
        display: none;
    }
    
    .comment-form:target {
        display: block;
    }
    
    .comment-close {
        position: absolute;
        top: 0.9375rem;
        right: 0.9375rem;
        color: #8899a6;
        text-decoration: none;
        font-size: 1.2em;
    }
    
    .comment-close:hover {
        color: #350835;
    }
    
    .comment-header h3 {
        color: #fff;
        margin: 0 0 0.9375rem 0;
        font-size: 1.1em;
    }
    
    .reply-username {
        color: #350835;
        font-weight: bold;
    }
    
    .comment-input {
        display: flex;
        gap: 0.625rem;
        margin-bottom: 15px;
    }
    
    .comment-avatar {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        object-fit: cover;
    }
    
    .comment-text {
        flex: 1;
        background: transparent;
        border: 1px solid white;
        border-radius: 0.625rem;
        padding: 0.625rem;
        color: #fff;
        font-size: 15px;
        resize: none;
        min-height: 100px;
    }
    
    .comment-actions {
        display: flex;
        justify-content: flex-end;
        gap: 0.625rem;
    }
    
    .comment-cancel {
        background: transparent;
        /* border: 1px solid #38444d; */
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 1.25rem;
        text-decoration: none;
        font-weight: bold;
    }
    
    .comment-submit {
        background: #350835;
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 1.25rem;
        font-weight: bold;
        cursor: pointer;
    }

    .post-input{
        display: flex;
        align-items: center;
        gap: 0.625rem;
    }

    .post-input img{
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
    }

    .post-input input{
        width: 100%;
        padding: 0.875rem 0.9375rem;
        border-radius: 0.625rem;
        border: none;
        font-size: 17px;
        background-color: #350835;
        color: white;
    }

    .post-types{
        display: flex;
        justify-content:space-around;
        padding-left: 3.5rem;
        margin-top: 10px;
    }

    .type{
        display: flex;
        align-items: center;
        gap: 0.625rem;
        padding: 0.75rem 1.25rem;
        border: 1px solid grey;
        border-radius: 1.5625rem;
        font-size: 13px;
        color: #F5F8FA;
        cursor: pointer;
    }

    .type:hover{
        background:#350835;
        border: 1px solid white;
        color: #F5F8FA;
    }

    .type p
    {
        margin: 0;
    }

    .type i{
        font-size: 15px;
    }

    /* .type:nth-child(1) i{
        color: rgb(1, 158, 48)
    }

    .type:nth-child(2) i{
        color: rgb(45, 114, 252)
    } */

    /* .type:nth-child(3) i{
        color: rgb(255, 110, 58)
    } */

    /* .type:nth-child(4) i{
        color: rgb(204, 165, 24)
    } */

    .trending{
        background-color: #522B5B;
        height: 37.5rem;
        border-radius: 0.75rem;
    }

    .trend-header{
        display: flex;
        justify-content: space-between;
        color: white;
        align-items: center;
        padding: 0.75rem;
    }

    .trend-header h4{
        margin: 0.3125rem 0;
    }

    .trend-category h3{
        color: #AAB8C2;
        font-size: 14px;
        font-weight: 300;
        padding: 0px 12px;
    }

    .trend {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0.625rem;
        cursor: pointer;
    }
    .trend:hover{
        background-color: #2e425d;
    }
    .trend h4{
        margin: 0.3125rem 0;
        color: white;
        font-size: 14px;
    }
    .trend p{
        margin: 0.125rem 0;
        color: #AAB8C2;
        font-size: 0.75rem;
    }
    .trend i {
        color: #AAB8C2;
        cursor: pointer;
    }
    .trend i:hover{
        color: white
    }
    .trending .show-more{
        padding: 0px 12px;
    }
    .chat-container {
        position: fixed;
        bottom: 0;
        right: 0;
        z-index: 9999;
        overflow: visible;
        cursor: pointer;
    }

    .chat-box {
        width: 300px;
        border-radius: 0.5rem;
        background-color: #15202b;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        font-family: Arial, sans-serif;
        animation: slideDown 0.3s ease-out forwards;
        transform-origin: bottom right;
        overflow: hidden;
        position: relative;
        top: 9.375rem;
    }

    .chat-container:hover .chat-box {
        animation: slideUp 0.3s ease-out forwards;
    }

    @keyframes slideUp {
        0% {
            transform: translateY(0%);
        }
        100% {
            transform: translateY(-100%);
        }
    }

    @keyframes slideDown {
        0% {
            transform: translateY(10%);
        }
        100% {
            transform: translateY(-10%);
        }
    }

    .chat-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.625rem 1.25rem;
        background-color: #192734;
        color: white;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }

    .chat-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
    }

    .close-button {
        border: none;
        background-color: transparent;
        font-size: 20px;
        font-weight: bold;
        color: white;
        cursor: pointer;
    }

    .chat-card {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 0.625rem;
        border-bottom: 1px solid #36444e;
    }

    .profile-image {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%;
        background-color: #657786;
    }

    .chat-content {
        margin-left: 0.625rem;
        flex-grow: 1;
    }

    .chat-content h4 {
        margin: 0;
        font-size: 14px;
        color: #350835;
    }

    .chat-content p {
        margin: 0;
        font-size: 0.75rem;
        color: #AAB8C2;
        text-overflow: ellipsis;
        width: 12.5rem;
        white-space: nowrap;
        overflow: hidden;
}

/* Post Toggle Checkbox (hidden) */
.post-toggle {
    display: none;
}

/* Floating Post Button */
.floating-post-button {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1001;
    cursor: pointer;
}

.floating-post-button .create-post-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: #350835;
    color: white;
    border: none;
    border-radius: 2rem;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    cursor: pointer;
}

.floating-post-button .create-post-btn:hover {
    background-color: #4a0e4a;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* Post Form Container */
.post-form-container {
    position: fixed;
    bottom: 6rem;
    right: 2rem;
    width: 90%;
    max-width: 400px;
    background: #FBE4D8;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform: translateY(20px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.post-form {
    padding: 1.5rem;
}

.post-form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    color:  #2B124C
}

.close-form {
    cursor: pointer;
    color: #666;
    font-size: 1.25rem;
    padding: 0.5rem;
}

.post-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    resize: none;
    font-family: inherit;
    margin-bottom: 1rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    padding-top: 1rem;
    clear: both;
}

.category-dropdown {
    position: relative;
    display: inline-block;
    z-index: 1000;
}

.category-toggle {
    display: none;
}

.btn-post, .btn-category {
    background-color: var(--color-primary);
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-category {
    background-color: var(--color-mid);
    margin-left: 0.5rem;
}

.btn-category:hover {
    background-color: var(--color-dark);
}

.dropdown-arrow {
    font-size: 0.6rem;
    margin-left: 0.25rem;
    transition: transform 0.2s ease;
}

.category-dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    bottom: 100%;
    margin-bottom: 10px;
    background-color: white;
    width: 250px;
    max-height: 300px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 0.5rem 0;
    border: 2px solid var(--color-primary);
    z-index: 1001;
}

.category-dropdown-content.show {
    display: block;
}

/* Dropdown overlay */
.dropdown-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    z-index: 9998;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-toggle:checked ~ .dropdown-overlay {
    display: block;
    opacity: 1;
}

/* Custom scrollbar */
.category-dropdown-content::-webkit-scrollbar {
    width: 6px;
}

.category-dropdown-content::-webkit-scrollbar-track {
    background: transparent;
}

.category-dropdown-content::-webkit-scrollbar-thumb {
    background-color: var(--color-mid);
    border-radius: 3px;
}


.category-toggle:checked + .btn-category .dropdown-arrow {
    transform: rotate(180deg);
}

.category-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    color: var(--color-text);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.95rem;
    white-space: nowrap;
    min-height: 40px;
    gap: 10px;
}

.category-item i {
    width: 20px;
    text-align: center;
    color: var(--color-primary);
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.category-item:hover {
    background-color: var(--color-hover);
}

.category-item:hover i {
    transform: scale(1.1);
}

.category-checkbox {
    margin-right: 0.5rem;
    width: 16px;
    height: 16px;
    accent-color: #190019;
    cursor: pointer;
}

/* Style for selected categories */
.category-item input[type="checkbox"]:checked + i {
    color: var(--color-primary);
    transform: scale(1.1);
}

.category-item input[type="checkbox"]:checked ~ span {
    font-weight: 600;
    color: var(--color-primary);
}

/* Custom checkbox style */
.category-checkbox {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid var(--color-mid);
    border-radius: 4px;
    outline: none;
    cursor: pointer;
    position: relative;
    vertical-align: middle;
    transition: all 0.2s ease;
}

.category-checkbox:checked {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.category-checkbox:checked::after {
    content: "✓";
    position: absolute;
    color: white;
    font-size: 12px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    line-height: 1;
}

/* Selected categories bubbles */
.selected-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
    min-height: 32px;
}

.category-bubble {
    display: inline-flex;
    align-items: center;
    background-color: var(--color-primary);
    color: white;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.85rem;
    gap: 6px;
    animation: bubbleIn 0.2s ease-out;
}

@keyframes bubbleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.category-bubble .remove-category {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0 0 0 4px;
    font-size: 0.9em;
    display: flex;
    align-items: center;
    opacity: 0.8;
}

.category-bubble .remove-category:hover {
    opacity: 1;
}

/* Hide the default radio button */
.category-radio {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid var(--color-border);
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative;
    vertical-align: middle;
}

.category-radio:checked {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.category-radio:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background-color: white;
    border-radius: 50%;
}

/* Close dropdown when clicking outside */
.category-dropdown:focus-within .category-dropdown-content {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.empty-div {
    display: none;
    width: 200px;
    height: 200px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    position: absolute;
    right: 20px;
    bottom: 70px;
    z-index: 1000;
}

.btn-category:focus + .empty-div,
.empty-div:focus-within {
    display: block;
}

/* Show/hide form with checkbox hack */
.post-toggle:checked ~ .post-form-container {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

/* Overlay when form is open */
.post-toggle:checked ~ .floating-post-button::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .floating-post-button {
        bottom: 1.5rem;
        right: 1.5rem;
    }
    
    .floating-post-button .create-post-btn {
        padding: 1rem;
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
    }
    
    .floating-post-button .create-post-btn span {
        display: none;
    }
    
    .floating-post-button .create-post-btn i {
        font-size: 1.2rem;
        margin: 0;
    }
    
    .post-form-container {
        bottom: 5.5rem;
        right: 1.5rem;
        width: calc(100% - 3rem);
    }
    
    .post-form {
        padding: 1rem;
    }
}

.floating-post-button {
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
}

.create-post-btn {
    background-color: #350835;
    color: white;
    border: none;
    border-radius: 2rem;
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
}

.create-post-btn:hover {
    background-color: #4a0c4a;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.create-post-btn i {
    font-size: 1.1rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #350835;
    padding: 1.5rem;
    border-radius: 1rem;
    width: 90%;
    max-width: 600px;
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
}

.close {
    position: absolute;
    right: 1.5rem;
    /* top: 1.5rem; */
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    background: none;
    border: none;
    /* padding: 0.5rem; */
    line-height: 1;
}

.close:hover {
    color: #333;
}

/* Make the post creation form look good in modal */
.modal .posts-creation {
    margin-top: 1.5rem;
    padding: 0;
    box-shadow: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .floating-post-button {
        bottom: 1.5rem;
        right: 1.5rem;
    }
    
    .create-post-btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.9rem;
    }
    
    .modal-content {
        width: 95%;
        padding: 1rem;
    }
}

header{
  position: sticky;
  top: 0;
  z-index: 1000;
}
.sidebar{
  position: sticky;
  top: 3.75rem;
  height: fit-content;
}
.trending{
  top: 3.75rem;
  height: fit-content;
  position: sticky;
}

@media only screen and (max-width: 600px) {
  .input-box{
    width: 15px !important;
    }
    header{
        padding: 0.3125rem 2%;
        width: 100%;
        background-color: #190019;
    }
    .type{
        padding: 0.5rem 1rem;
        font-size: 11px;
    }
    main{
        display: block;
        padding: 0.625rem 0;
    }
    .sidebar, .trending, .chat-container{
        display: none;
    }   
}

/* log-sign.html */

/* From Uiverse.io by SelfMadeSystem */ 
.form-container {
  position: relative;
  perspective: 50rem;
  width: 50rem;
  height: 50rem;
}

#switch {
  display: none;
}

.sign-up-form,
.log-in-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sign-up-container,
.log-in-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  inset: 0;
  margin: auto;
  width: 20rem;
  height: 20rem;
  background: #2c164f;
  color: #fff;
  padding: 1rem;
  border-radius: 0.5rem;
}

.switch-label {
  font-size: x-small;
  text-decoration: underline;
  cursor: pointer;
}

.form-title {
  font-size: x-large;
  font-weight: bold;
}

:where(.sign-up-form, .log-in-form) label {
  font-size: x-small;
  padding-top: 0.5rem;
  padding-left: 0.25rem;
}

:where(.sign-up-form, .log-in-form) input {
  background-color: #fff2;
  box-sizing: border-box;
  color: white;
  border: 0;
  border-bottom: 2px solid #cfb2ff;
  padding: 0.25rem;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

:where(.sign-up-form, .log-in-form) input:focus-visible {
  outline: none;
  border: 1px solid white;
  border-bottom: 2px solid #cfb2ff;
}

:where(.sign-up-form, .log-in-form) button {
  margin-top: auto;
  background-color: #ffffff4b;
  color: white;
  border: 0;
  padding: 0.25rem;
  border-radius: 0.25rem;
  cursor: pointer;
}

.forgot-pwd {
  font-size: x-small;
  text-decoration: underline;
  margin-left: auto;
  cursor: pointer;
}

/* Social yoinked from uiverse.io/Yaya12085/short-panda-24 */

.social-message {
  display: flex;
  align-items: center;
  padding-top: 1rem;
}

.line {
  height: 1px;
  flex: 1 1 0%;
  background-color: #cfb2ff;
}

.social-message .message {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #fff;
}

.social-icons {
  display: flex;
  justify-content: center;
}

.social-icons .icon {
  border-radius: 0.125rem;
  padding: 0.75rem;
  padding-bottom: 0.25rem;
  border: none;
  background-color: transparent;
  margin-left: 8px;
  cursor: pointer;
}

.social-icons .icon svg {
  height: 1.25rem;
  width: 1.25rem;
  fill: #fff;
}

.sign-up-container {
  animation: hello 0.5s linear forwards;
}

.log-in-container {
  animation: unhello 0.5s linear forwards;
}

#switch:checked ~ .sign-up-container {
  animation: unhello 0.5s linear forwards;
}

#switch:checked ~ .log-in-container {
  animation: hello 0.5s linear forwards;
}

@keyframes hello {
  0% {
    transform: rotateY(-90deg);
    display: none;
  }
  50% {
    transform: rotateY(-90deg);
    display: flex;
  }
  100% {
    transform: rotateY(0deg);
    display: flex;
  }
}

@keyframes unhello {
  0% {
    transform: rotateY(0deg);
    display: flex;
  }
  50% {
    transform: rotateY(90deg);
    display: flex;
  }
  100% {
    transform: rotateY(90deg);
    display: none;
  }
}

/* From Uiverse.io by javierBarroso */ 
.wrapper {
  --background: #62abff;
  --icon-color: #414856;
  --shape-color-01: #b8cbee;
  --shape-color-02: #7691e8;
  --shape-color-03: #fdd053;
  --width: 90px;
  --height: 90px;
  --border-radius: var(--height);
  width: var(--width);
  height: var(--height);
  position: relative;
  border-radius: var(--border-radius);
  display: flex;
  justify-content: center;
  align-items: center;
}
.wrapper .btn {
  background: var(--background);
  width: var(--width);
  height: var(--height);
  position: relative;
  z-index: 3;
  border-radius: var(--border-radius);
  box-shadow: 0 10px 30px rgba(65, 72, 86, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-animation: plus-animation-reverse 0.5s ease-out forwards;
  animation: plus-animation-reverse 0.5s ease-out forwards;
}
.wrapper .btn::before,
.wrapper .btn::after {
  content: "";
  display: block;
  position: absolute;
  border-radius: 4px;
  background: #fff;
}
.wrapper .btn::before {
  width: 4px;
  height: 28px;
}
.wrapper .btn::after {
  width: 28px;
  height: 4px;
}
.wrapper .tooltip {
  width: fit-content;
  height: 4.6875rem;
  border-radius: 70px;
  position: absolute;
  background: #fff;
  z-index: 2;
  padding: 20px 35px;
  box-shadow: 0 10px 30px rgba(65, 72, 86, 0.05);
  opacity: 0;
  top: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  transition: opacity 0.15s ease-in, top 0.15s ease-in, width 0.15s ease-in;
}
.wrapper .tooltip > span {
  position: relative;
  width: 100%;
  white-space: nowrap;
  opacity: 0;
}
.wrapper .tooltip > svg {
  width: 100%;
  height: 26px;
  display: block;
  justify-content: space-around;
  align-items: center;
  cursor: pointer;
}
.wrapper .tooltip > svg .icon {
  fill: none;
  stroke: var(--icon-color);
  stroke-width: 2px;
  stroke-linecap: round;
  stroke-linejoin: round;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}
.wrapper .tooltip > svg:hover .icon {
  opacity: 1;
}
.wrapper .tooltip::after {
  content: "";
  width: 20px;
  height: 20px;
  background: #fff;
  border-radius: 3px;
  position: absolute;
  left: 50%;
  margin-left: -10px;
  bottom: -8px;
  transform: rotate(45deg);
  z-index: 0;
}
.wrapper > svg {
  width: 300px;
  height: 300px;
  position: absolute;
  z-index: 1;
  transform: scale(0);
}
.wrapper > svg .shape {
  fill: none;
  stroke: none;
  stroke-width: 3px;
  stroke-linecap: round;
  stroke-linejoin: round;
  transform-origin: 50% 20%;
}
.wrapper input {
  height: 100%;
  width: 100%;
  border-radius: var(--border-radius);
  cursor: pointer;
  position: absolute;
  z-index: 5;
  opacity: 0;
}
.wrapper input:checked ~ svg {
  -webkit-animation: pang-animation 1.2s ease-out forwards;
  animation: pang-animation 1.2s ease-out forwards;
}
.wrapper input:checked ~ svg .shape:nth-of-type(1) {
  transform: translate(25px, 30%) rotate(40deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(2) {
  transform: translate(-4px, 30%) rotate(80deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(3) {
  transform: translate(12px, 30%) rotate(120deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(4) {
  transform: translate(8px, 30%) rotate(160deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(5) {
  transform: translate(21px, 30%) rotate(200deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(6) {
  transform: translate(0px, 30%) rotate(240deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(7) {
  transform: translate(17px, 30%) rotate(280deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(8) {
  transform: translate(-3px, 30%) rotate(320deg);
}
.wrapper input:checked ~ svg .shape:nth-of-type(9) {
  transform: translate(25px, 30%) rotate(360deg);
}
.wrapper input:checked ~ .btn {
  -webkit-animation: plus-animation 0.5s ease-out forwards;
  animation: plus-animation 0.5s ease-out forwards;
}
.wrapper input:checked ~ .tooltip {
  width: fit-content;
  padding: 30px;
  -webkit-animation: stretch-animation 1s ease-out forwards 0.15s;
  animation: stretch-animation 1s ease-out forwards 0.15s;
  top: -90px;
  opacity: 1;
}

.wrapper input:checked ~ .tooltip > span {
  opacity: 1;
}

@-webkit-keyframes pang-animation {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}

@keyframes pang-animation {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}
@-webkit-keyframes plus-animation {
  0% {
    transform: rotate(0) scale(1);
  }
  20% {
    transform: rotate(60deg) scale(0.93);
  }
  55% {
    transform: rotate(35deg) scale(0.97);
  }
  80% {
    transform: rotate(48deg) scale(0.94);
  }
  100% {
    transform: rotate(45deg) scale(0.95);
  }
}
@keyframes plus-animation {
  0% {
    transform: rotate(0) scale(1);
  }
  20% {
    transform: rotate(60deg) scale(0.93);
  }
  55% {
    transform: rotate(35deg) scale(0.97);
  }
  80% {
    transform: rotate(48deg) scale(0.94);
  }
  100% {
    transform: rotate(45deg) scale(0.95);
  }
}
@-webkit-keyframes plus-animation-reverse {
  0% {
    transform: rotate(45deg) scale(0.95);
  }
  20% {
    transform: rotate(-15deg);
  }
  55% {
    transform: rotate(10deg);
  }
  80% {
    transform: rotate(-3deg);
  }
  100% {
    transform: rotate(0) scale(1);
  }
}
@keyframes plus-animation-reverse {
  0% {
    transform: rotate(45deg) scale(0.95);
  }
  20% {
    transform: rotate(-15deg);
  }
  55% {
    transform: rotate(10deg);
  }
  80% {
    transform: rotate(-3deg);
  }
  100% {
    transform: rotate(0) scale(1);
  }
}
@-webkit-keyframes stretch-animation {
  0% {
    transform: scale(1, 1);
  }
  10% {
    transform: scale(1.1, 0.9);
  }
  30% {
    transform: scale(0.9, 1.1);
  }
  50% {
    transform: scale(1.05, 0.95);
  }
  100% {
    transform: scale(1, 1);
  }
}
@keyframes stretch-animation {
  0% {
    transform: scale(1, 1);
  }
  10% {
    transform: scale(1.1, 0.9);
  }
  30% {
    transform: scale(0.9, 1.1);
  }
  50% {
    transform: scale(1.05, 0.95);
  }
  100% {
    transform: scale(1, 1);
  }
}

.socials {
  position: fixed;
  display: block;
  left: 20px;
  bottom: 20px;
}
.socials > a {
  display: block;
  width: 30px;
  opacity: 0.2;
  transform: scale(var(--scale, 0.8));
  transition: transform 0.3s cubic-bezier(0.38, -0.12, 0.24, 1.91);
}
.socials > a:hover {
  --scale: 1;
}

.header-space {
  margin-left: 2rem;
  margin-right: 2rem;
}

footer{
  color: white;
  text-align: center;
}

main {
    width: 60%;
    max-width: 1000px;
    min-width: 280px;
    /* background: #350835;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    padding: 2rem; */
    display: block;
}

@media (max-width: 900px) {
    main {
        width: 90%;
        padding: 1rem;
    }
}