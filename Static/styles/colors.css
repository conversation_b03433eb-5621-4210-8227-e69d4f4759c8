:root {
    /* Dark to light color palette */
    /* --color-darkest: #190019; */
    --color-darker: #2B124C;
    --color-dark: #522B5B;
    --color-mid: #854F6C;
    --color-light: #DFB6B2;
    --color-lightest: #FBE4D8;
    
    /* Semantic color variables */
    --color-primary: var(--color-darker);
    --color-secondary: var(--color-mid);
    --color-accent: var(--color-light);
    --color-background: var(--color-lightest);
    --color-text: var(--color-darkest);
    --color-text-light: var(--color-mid);
    
    /* UI Colors */
    --color-border: var(--color-light);
    --color-hover: rgba(139, 92, 108, 0.1);
    --color-shadow: rgba(25, 0, 25, 0.1);
    
    /* Status Colors */
    --color-success: #4CAF50;
    --color-warning: #FFC107;
    --color-error: #F44336;
    --color-info: #2196F3;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--color-darker), var(--color-mid));
    --gradient-subtle: linear-gradient(135deg, var(--color-light), var(--color-lightest));
}
