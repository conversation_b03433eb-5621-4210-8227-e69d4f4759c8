/* Using color variables from colors.css */
:root {
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --radius-sm: 8px;
    --radius-md: 12px;
    --transition: all 0.2s ease-in-out;
}

/* Base container */
.posts-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    min-height: 100vh;
    /* background: var(--gradient-primary); */
    color: var(--color-text);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Post wrapper */
.post-wrapper {
    background: var(--color-lightest);
    border-radius: 16px;
    box-shadow: 0 8px 24px var(--color-shadow);
    overflow: hidden;
    transition: var(--transition);
    border: none;
    padding: 2rem;
    margin-bottom: 1.5rem;
    position: relative;
    padding-top: 50px; 
}

.post-wrapper:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* Post content */
.post {
    padding: 0;
    background: transparent;
    border-radius: 0;
}

.post-header {
    margin-bottom: 1rem;
}

.post-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    color: var(--color-darker);
    font-weight: 600;
    line-height: 1.3;
}

.post-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--color-text-lighter);
    font-size: 0.9rem;
}

.post-author {
    font-weight: 500;
    color: var(--color-dark);
}

.post-date {
    color: var(--color-mid);
    font-size: 0.85em;
}

/* Post Header */
.post-header {
    position: relative;
    margin-bottom: 1rem;
}

.post-header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

/* Category bubble container */
.category-bubble-container {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    gap: 0.5rem;
}

.comment-login-bubble {

}

/* Category bubble styling */
.category-bubble {
    display: inline-flex !important;
    align-items: center;
    /* background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark)); */
    color: white !important;
    padding: 0.4rem 0.9rem !important;
    border-radius: 1rem !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    white-space: nowrap;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none !important;
    text-decoration: none !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-transform: capitalize;
    margin: 0;
}

.category-bubble:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary));
    animation: bubbleFloat 1.5s ease-in-out infinite;
}

@keyframes bubbleFloat {
    0% { transform: translateY(-2px) scale(1.05); }
    50% { transform: translateY(-5px) scale(1.05); }
    100% { transform: translateY(-2px) scale(1.05); }
}

/* Post content */
.post-content {
    color: var(--color-text);
    line-height: 1.7;
    margin: 1.5rem 0;
    font-size: 1.05rem;
    /* padding: 1rem 0;
    border-top: 1px solid var(--color-border);
    border-bottom: 1px solid var(--color-border); */
}

/* Post actions */
.post-actions {
    display: flex;
    gap: 1.5rem;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    color: var(--color-mid);
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: var(--transition);
      text-decoration: none;

}

.action-btn:hover {
    background: rgba(0, 0, 0, 0.05);
    color: var(--color-darker);
}

.action-btn i {
    font-size: 1.1em;
    color: var(--color-mid);
}

.action-btn:hover i {
    color: var(--color-darker);
}

/* No posts message */
.no-posts {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--color-text-lighter);
    background: var(--color-white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-border);
}

/* .comment-form {
    margin: 0.5rem 0 1rem 0;
    background: transparent;
    position: relative;
    opacity: 1;
    visibility: visible;
    max-height: none;
    overflow: visible;
}
 */


.back-button {
    position: absolute;
    top: 12px;   
    left: 12px; 
    padding: 6px 14px;
    font-size: 15px;
    font-weight: bold;
    color: #2B124C ;
    border-radius: 6px;
    text-decoration: none;
    transition: background 0.2s ease, transform 0.1s ease;
}

.back-button:hover {
    transform: translateY(-2px);
}




.comment-header {
    display: none;
}

.comment-input {
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.comment-text {
    min-height: 60px;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    border-radius: 6px;
    border: 1px solid var(--color-border);
    background: var(--color-lightest);
}

.comment-actions {
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 0.25rem;
}

.comment-cancel,
.comment-submit {
    font-size: 0.85rem;
    padding: 0.3rem 0.8rem;
}

.comment-submit {
    background: var(--color-primary);
    color: white;
}

.comment-submit:hover {
    background: var(--color-primary-dark);
}

.comment-close {
    position: absolute;
    top: 12px;
    right: 12px;
    color: var(--color-mid);
    font-size: 1.1rem;
    z-index: 1;
    transition: var(--transition);
}

.comment-close:hover {
    color: var(--color-darker);
}


.comment-header h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    color: var(--color-darker);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.comment-input {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.comment-text {
    flex: 1;
    min-height: 100px;
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.95rem;
    line-height: 1.5;
    resize: vertical;
    transition: var(--transition);
}

.comment-text:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(138, 79, 255, 0.1);
}

.comment-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 0.5rem;
}

.comment-cancel {
    background: none;
    border: none;
    color: var(--color-mid);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.comment-cancel:hover {
    background: rgba(0, 0, 0, 0.05);
    color: var(--color-darker);
}

.comment-submit {
    background: var(--color-primary);
    color: white;
    border: none;
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.comment-submit:hover {
    background: var(--color-primary-dark);
    transform: translateY(-1px);
}

/* Comments Section */
.comments-section {
    margin-top: 2.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.comments-section h3 {
    font-size: 1.25rem;
    color: var(--color-darker);
    margin-bottom: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.comment-list {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.comment {
    background: var(--color-lightest);
    border-radius: 10px;
    padding: 1.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.comment:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.comment strong {
    color: var(--color-darker);
    font-size: 0.95rem;
}

.comment-date {
    font-size: 0.8rem;
    color: var(--color-mid);
    margin-left: 0.75rem;
    font-weight: normal;
}

.comment p {
    margin: 0.75rem 0 0 0;
    color: var(--color-text);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Comment Form */
/* .comments-section form {
    background: var(--color-lightest);
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-top: 1.5rem;
} */

.comments-section form textarea {
    width: 100%;
    min-height: 100px;
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.95rem;
    line-height: 1.5;
    resize: vertical;
    margin-bottom: 1rem;
    transition: var(--transition);
    background: var(--color-white);
}

.comments-section form textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(138, 79, 255, 0.1);
}

.comments-section form button[type="submit"] {
    background: var(--color-primary);
    color: white;
    border: none;
    padding: 0.6rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.95rem;
}

.comments-section form button[type="submit"]:hover {
    background: var(--color-primary-dark);
    transform: translateY(-1px);
}

/* No comments message */
.comments-section > p {
    color: var(--color-mid);
    font-style: italic;
    margin: 1rem 0 2rem;
}

/* Login prompt */
.comments-section > p > a {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.comments-section > p > a:hover {
    color: var(--color-primary-dark);
    text-decoration: underline;
}

/* Home Link */
.home-link {
    display: inline-block;
    font-size: 20px;
    padding: 12px 24px;
    margin: 20px 0;
    border: 2px solid #000;
    color: #000;
    background-color: transparent;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease-in-out;
    text-align: center;
}

.home-link:hover,
.home-link:active,
.home-link:focus {
    color: #fff;
    background: #000;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.home-link:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
    .posts-container {
        padding: 0 0.75rem;
        gap: 1rem;
    }
    
    .post {
        padding: 1.25rem;
    }
    
    .post-title {
        font-size: 1.3rem;
    }
    
    .post-content {
        font-size: 1rem;
        margin: 1.25rem 0;
    }
    
    .post-actions {
        gap: 0.5rem;
    }
    
    .action-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
}
