- [ ] make go templates (<PERSON><PERSON>)
- [ ] assign pathes and function handlers (mariam)
- [ ] user fetching functions (sara)
- [ ] finish http routing (mariam)
- [ ] encrypt passwords (hussain)
- [ ] filter by category (sara)
- [ ] authentication (hussain)
- [ ] session tokens (hussain)


- [ ]like
- [ ] dislike
- [ ] my liked post 
- [ ] filer by cat
- [ ] fix filter css

- [ ] handle new lines in posts